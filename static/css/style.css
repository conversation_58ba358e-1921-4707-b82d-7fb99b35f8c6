/* Sensor Calibration Tool Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    overflow: hidden;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.header {
    background-color: #2c3e50;
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 300;
}

.header-controls {
    display: flex;
    gap: 1rem;
}

/* Main Content Layout */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.left-panel {
    width: 300px;
    background-color: white;
    border-right: 1px solid #ddd;
    overflow-y: auto;
    padding: 1rem;
}

.center-panel {
    flex: 1;
    background-color: #2c3e50;
    position: relative;
}

.right-panel {
    width: 400px;
    background-color: white;
    border-left: 1px solid #ddd;
    position: relative;
}

/* Panel Sections */
.panel-section {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.panel-section:last-child {
    border-bottom: none;
}

.panel-section h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
}

/* Upload Sections */
.upload-section {
    margin-bottom: 1rem;
}

.upload-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.upload-section input[type="file"] {
    width: 100%;
    padding: 0.5rem;
    border: 2px dashed #ddd;
    border-radius: 4px;
    background-color: #fafafa;
    cursor: pointer;
    transition: border-color 0.3s;
}

.upload-section input[type="file"]:hover {
    border-color: #3498db;
}

.status-info {
    margin-top: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.status-info.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-info.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-info.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Control Groups */
.control-group {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-group label {
    font-weight: 500;
    color: #555;
    min-width: 100px;
}

.control-group input[type="range"] {
    flex: 1;
}

.control-group input[type="checkbox"] {
    margin-right: 0.5rem;
}

/* Calibration Mode */
.calibration-mode {
    margin-bottom: 1rem;
}

.calibration-mode label {
    display: block;
    margin-bottom: 0.5rem;
    cursor: pointer;
}

.calibration-mode input[type="radio"] {
    margin-right: 0.5rem;
}

/* Correspondence Info */
.correspondence-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.correspondence-info p {
    font-weight: 500;
    color: #2c3e50;
}

/* Correspondence List */
.correspondence-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0.5rem;
}

.correspondence-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 0.9rem;
}

.correspondence-item:last-child {
    margin-bottom: 0;
}

.correspondence-coords {
    flex: 1;
}

.correspondence-remove {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 0.2rem;
}

.correspondence-remove:hover {
    color: #c82333;
}

/* Buttons */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: background-color 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #5a6268;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: #218838;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover:not(:disabled) {
    background-color: #e0a800;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: #c82333;
}

/* Visualization Container */
.visualization-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.threejs-container {
    width: 100%;
    height: 100%;
}

.threejs-container canvas {
    display: block;
    width: 100%;
    height: 100%;
}

/* Image Container */
.image-container {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-canvas {
    max-width: 100%;
    max-height: 100%;
    cursor: crosshair;
    border: 1px solid #ddd;
}

/* Info Overlays */
.info-overlay {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
    font-family: monospace;
    pointer-events: none;
    z-index: 1000;
}

.visualization-info .info-overlay {
    background-color: rgba(255, 255, 255, 0.9);
    color: #333;
}

/* Status Bar */
.status-bar {
    background-color: #34495e;
    color: white;
    padding: 0.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

/* Loading Indicator */
.loading-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff40;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Calibration Results */
#calibrationResults {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
}

.calibration-metric {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.calibration-metric .label {
    font-weight: 500;
    color: #555;
}

.calibration-metric .value {
    font-family: monospace;
    color: #2c3e50;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .left-panel {
        width: 250px;
    }
    
    .right-panel {
        width: 350px;
    }
}

@media (max-width: 900px) {
    .main-content {
        flex-direction: column;
    }
    
    .left-panel,
    .right-panel {
        width: 100%;
        height: 200px;
    }
    
    .center-panel {
        height: 400px;
    }
}

/* Matrix and Vector Display */
.matrix-display {
    font-family: monospace;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
    margin: 0.5rem 0;
    overflow-x: auto;
}

.matrix-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.2rem;
}

.matrix-element {
    display: inline-block;
    width: 120px;
    text-align: right;
    padding: 0.2rem;
    background-color: white;
    border: 1px solid #ddd;
    margin: 1px;
    border-radius: 2px;
}

.vector-display {
    font-family: monospace;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
    margin: 0.5rem 0;
}

.calibration-metrics h4 {
    color: #2c3e50;
    margin: 1rem 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
