/**
 * Point Cloud Viewer using Three.js
 */

class PointCloudViewer {
    constructor(containerId) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        
        // Three.js components
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        
        // Point cloud data
        this.pointCloud = null;
        this.pointCloudData = null;
        this.originalColors = null;
        
        // Interaction
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        this.selectedPoint = null;
        this.selectionMode = false;
        
        // Visual elements
        this.axes = null;
        this.grid = null;
        
        // Settings
        this.pointSize = 2;
        this.showIntensity = true;
        this.showAxes = true;
        
        this.init();
        this.setupEventListeners();
    }
    
    init() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x2c3e50);
        
        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75,
            this.container.clientWidth / this.container.clientHeight,
            0.1,
            1000
        );
        this.camera.position.set(10, 10, 10);
        
        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.container.appendChild(this.renderer.domElement);
        
        // Create controls
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.screenSpacePanning = false;
        this.controls.minDistance = 1;
        this.controls.maxDistance = 500;
        
        // Add lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        this.scene.add(directionalLight);
        
        // Add coordinate axes
        this.createAxes();
        
        // Add grid
        this.createGrid();
        
        // Start render loop
        this.animate();
    }
    
    createAxes() {
        this.axes = new THREE.AxesHelper(5);
        this.scene.add(this.axes);
    }
    
    createGrid() {
        this.grid = new THREE.GridHelper(20, 20, 0x888888, 0x444444);
        this.scene.add(this.grid);
    }
    
    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Mouse events for point selection
        this.renderer.domElement.addEventListener('click', (event) => this.onMouseClick(event));
        this.renderer.domElement.addEventListener('mousemove', (event) => this.onMouseMove(event));
        
        // Listen for mode changes
        eventBus.on('selectionModeChanged', (enabled) => {
            this.selectionMode = enabled;
            this.renderer.domElement.style.cursor = enabled ? 'crosshair' : 'default';
        });
        
        // Listen for settings changes
        eventBus.on('pointSizeChanged', (size) => this.setPointSize(size));
        eventBus.on('intensityToggled', (show) => this.toggleIntensity(show));
        eventBus.on('axesToggled', (show) => this.toggleAxes(show));
    }
    
    onWindowResize() {
        this.camera.aspect = this.container.clientWidth / this.container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    }
    
    onMouseClick(event) {
        if (!this.selectionMode || !this.pointCloud) return;
        
        // Calculate mouse position in normalized device coordinates
        const rect = this.renderer.domElement.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
        
        // Raycast to find intersected points
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObject(this.pointCloud);
        
        if (intersects.length > 0) {
            const intersection = intersects[0];
            const pointIndex = intersection.index;
            
            if (this.pointCloudData && pointIndex < this.pointCloudData.points.length) {
                const point = this.pointCloudData.points[pointIndex];
                
                // Emit point selection event
                eventBus.emit('pointSelected', {
                    point: point,
                    index: pointIndex,
                    worldPosition: intersection.point
                });
                
                // Highlight selected point
                this.highlightPoint(pointIndex);
            }
        }
    }
    
    onMouseMove(event) {
        if (!this.pointCloud) return;
        
        // Calculate mouse position
        const rect = this.renderer.domElement.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
        
        // Raycast for hover information
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObject(this.pointCloud);
        
        if (intersects.length > 0) {
            const intersection = intersects[0];
            const pointIndex = intersection.index;
            
            if (this.pointCloudData && pointIndex < this.pointCloudData.points.length) {
                const point = this.pointCloudData.points[pointIndex];
                const intensity = this.pointCloudData.intensities ? 
                    this.pointCloudData.intensities[pointIndex] : null;
                
                // Update info display
                this.updatePointInfo(point, intensity, pointIndex);
            }
        } else {
            this.clearPointInfo();
        }
    }
    
    loadPointCloud(data) {
        try {
            // Remove existing point cloud
            if (this.pointCloud) {
                this.scene.remove(this.pointCloud);
                this.pointCloud.geometry.dispose();
                this.pointCloud.material.dispose();
            }
            
            this.pointCloudData = data;
            
            // Create geometry
            const geometry = new THREE.BufferGeometry();
            
            // Set positions
            const positions = new Float32Array(data.points.flat());
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            
            // Set colors
            let colors;
            if (data.colors && this.showIntensity) {
                colors = new Float32Array(data.colors.flat());
            } else if (data.intensities && this.showIntensity) {
                // Convert intensities to colors
                colors = this.intensitiesToColors(data.intensities);
            } else {
                // Default gray color
                colors = new Float32Array(data.points.length * 3);
                for (let i = 0; i < colors.length; i += 3) {
                    colors[i] = 0.7;     // R
                    colors[i + 1] = 0.7; // G
                    colors[i + 2] = 0.7; // B
                }
            }
            
            geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
            this.originalColors = colors.slice(); // Store original colors
            
            // Create material
            const material = new THREE.PointsMaterial({
                size: this.pointSize,
                vertexColors: true,
                sizeAttenuation: false
            });
            
            // Create point cloud
            this.pointCloud = new THREE.Points(geometry, material);
            this.scene.add(this.pointCloud);
            
            // Center camera on point cloud
            this.centerCamera();
            
            Utils.updateStatus(`Loaded point cloud with ${data.points.length} points`, 'success');
            
            return true;
        } catch (error) {
            console.error('Error loading point cloud:', error);
            Utils.updateStatus('Failed to load point cloud', 'error');
            return false;
        }
    }
    
    intensitiesToColors(intensities) {
        const colors = new Float32Array(intensities.length * 3);
        const min = Math.min(...intensities);
        const max = Math.max(...intensities);
        
        for (let i = 0; i < intensities.length; i++) {
            const color = Utils.valueToColor(intensities[i], min, max);
            colors[i * 3] = color.r / 255;
            colors[i * 3 + 1] = color.g / 255;
            colors[i * 3 + 2] = color.b / 255;
        }
        
        return colors;
    }
    
    centerCamera() {
        if (!this.pointCloudData) return;
        
        const bounds = this.pointCloudData.bounds;
        if (!bounds) return;
        
        const center = new THREE.Vector3(
            bounds.center[0],
            bounds.center[1],
            bounds.center[2]
        );
        
        const size = Math.max(
            bounds.max[0] - bounds.min[0],
            bounds.max[1] - bounds.min[1],
            bounds.max[2] - bounds.min[2]
        );
        
        const distance = size * 2;
        
        this.camera.position.set(
            center.x + distance,
            center.y + distance,
            center.z + distance
        );
        
        this.controls.target.copy(center);
        this.controls.update();
    }
    
    highlightPoint(index) {
        if (!this.pointCloud || !this.originalColors) return;
        
        const colors = this.pointCloud.geometry.attributes.color.array;
        
        // Reset all colors
        for (let i = 0; i < colors.length; i++) {
            colors[i] = this.originalColors[i];
        }
        
        // Highlight selected point in red
        colors[index * 3] = 1.0;     // R
        colors[index * 3 + 1] = 0.0; // G
        colors[index * 3 + 2] = 0.0; // B
        
        this.pointCloud.geometry.attributes.color.needsUpdate = true;
        this.selectedPoint = index;
    }
    
    updatePointInfo(point, intensity, index) {
        const infoElement = document.getElementById('pointInfo');
        if (!infoElement) return;
        
        let info = `Point ${index}\n`;
        info += `X: ${Utils.formatNumber(point[0])}\n`;
        info += `Y: ${Utils.formatNumber(point[1])}\n`;
        info += `Z: ${Utils.formatNumber(point[2])}`;
        
        if (intensity !== null) {
            info += `\nIntensity: ${Utils.formatNumber(intensity)}`;
        }
        
        infoElement.textContent = info;
        infoElement.style.display = 'block';
    }
    
    clearPointInfo() {
        const infoElement = document.getElementById('pointInfo');
        if (infoElement) {
            infoElement.style.display = 'none';
        }
    }
    
    setPointSize(size) {
        this.pointSize = size;
        if (this.pointCloud) {
            this.pointCloud.material.size = size;
        }
    }
    
    toggleIntensity(show) {
        this.showIntensity = show;
        if (this.pointCloudData) {
            this.loadPointCloud(this.pointCloudData); // Reload with new color settings
        }
    }
    
    toggleAxes(show) {
        this.showAxes = show;
        if (this.axes) {
            this.axes.visible = show;
        }
    }
    
    resetView() {
        this.centerCamera();
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        this.controls.update();
        this.renderer.render(this.scene, this.camera);
    }
    
    dispose() {
        // Clean up resources
        if (this.pointCloud) {
            this.scene.remove(this.pointCloud);
            this.pointCloud.geometry.dispose();
            this.pointCloud.material.dispose();
        }
        
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        // Remove event listeners
        window.removeEventListener('resize', this.onWindowResize);
    }
}

// Export for global use
window.PointCloudViewer = PointCloudViewer;
