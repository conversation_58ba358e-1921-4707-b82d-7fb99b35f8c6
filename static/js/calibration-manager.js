/**
 * Calibration Manager - Handles correspondence points and calibration computation
 */

class CalibrationManager {
    constructor() {
        // Data storage
        this.pointcloudId = null;
        this.imageId = null;
        this.correspondences = [];
        this.calibrationResult = null;
        
        // UI elements
        this.correspondenceList = document.getElementById('correspondenceList');
        this.correspondenceCount = document.getElementById('correspondenceCount');
        this.computeButton = document.getElementById('computeCalibration');
        this.clearButton = document.getElementById('clearCorrespondences');
        this.exportButton = document.getElementById('exportCalibration');
        this.calibrationResults = document.getElementById('calibrationResults');
        this.calibrationInfo = document.getElementById('calibrationInfo');
        
        // Temporary storage for pending correspondence
        this.pendingPointCloud = null;
        this.pendingImage = null;
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Listen for point selections
        eventBus.on('pointSelected', (data) => this.onPointCloudPointSelected(data));
        eventBus.on('imagePointSelected', (data) => this.onImagePointSelected(data));
        
        // Listen for point removals
        eventBus.on('imagePointRemoved', (data) => this.onImagePointRemoved(data));
        
        // Listen for data loading
        eventBus.on('pointcloudLoaded', (data) => this.onPointCloudLoaded(data));
        eventBus.on('imageLoaded', (data) => this.onImageLoaded(data));
        
        // Button event listeners
        if (this.computeButton) {
            this.computeButton.addEventListener('click', () => this.computeCalibration());
        }
        
        if (this.clearButton) {
            this.clearButton.addEventListener('click', () => this.clearCorrespondences());
        }
        
        if (this.exportButton) {
            this.exportButton.addEventListener('click', () => this.exportCalibration());
        }
    }
    
    onPointCloudLoaded(data) {
        this.pointcloudId = data.data_id;
        this.updateUI();
    }
    
    onImageLoaded(data) {
        this.imageId = data.data_id;
        this.updateUI();
    }
    
    onPointCloudPointSelected(data) {
        this.pendingPointCloud = {
            point: data.point,
            index: data.index,
            worldPosition: data.worldPosition
        };
        
        // Check if we have a pending image point to create correspondence
        if (this.pendingImage) {
            this.createCorrespondence();
        } else {
            Utils.updateStatus('Point cloud point selected. Now select corresponding image point.', 'info');
        }
    }
    
    onImagePointSelected(data) {
        this.pendingImage = {
            point: data.point,
            index: data.index
        };
        
        // Check if we have a pending point cloud point to create correspondence
        if (this.pendingPointCloud) {
            this.createCorrespondence();
        } else {
            Utils.updateStatus('Image point selected. Now select corresponding point cloud point.', 'info');
        }
    }
    
    onImagePointRemoved(data) {
        // Find and remove corresponding correspondence
        const correspondenceIndex = this.correspondences.findIndex(
            corr => corr.imagePoint[0] === data.point[0] && corr.imagePoint[1] === data.point[1]
        );
        
        if (correspondenceIndex !== -1) {
            this.correspondences.splice(correspondenceIndex, 1);
            this.updateCorrespondenceList();
            this.updateUI();
        }
    }
    
    createCorrespondence() {
        if (!this.pendingPointCloud || !this.pendingImage) return;
        
        const correspondence = {
            id: Utils.generateUUID(),
            pointCloudPoint: this.pendingPointCloud.point,
            imagePoint: this.pendingImage.point,
            pointCloudIndex: this.pendingPointCloud.index,
            imageIndex: this.pendingImage.index,
            timestamp: Date.now()
        };
        
        this.correspondences.push(correspondence);
        
        // Send to backend
        this.addCorrespondenceToBackend(correspondence);
        
        // Clear pending points
        this.pendingPointCloud = null;
        this.pendingImage = null;
        
        // Update UI
        this.updateCorrespondenceList();
        this.updateUI();
        
        Utils.updateStatus(
            `Added correspondence ${this.correspondences.length}: ` +
            `PCD(${Utils.formatNumber(correspondence.pointCloudPoint[0])}, ` +
            `${Utils.formatNumber(correspondence.pointCloudPoint[1])}, ` +
            `${Utils.formatNumber(correspondence.pointCloudPoint[2])}) ↔ ` +
            `IMG(${correspondence.imagePoint[0]}, ${correspondence.imagePoint[1]})`,
            'success'
        );
    }
    
    async addCorrespondenceToBackend(correspondence) {
        try {
            const response = await Utils.makeRequest('/api/calibration/points', {
                method: 'POST',
                body: JSON.stringify({
                    pointcloud_id: this.pointcloudId,
                    image_id: this.imageId,
                    pcd_point: correspondence.pointCloudPoint,
                    image_point: correspondence.imagePoint
                })
            });
            
            if (!response.success) {
                console.error('Failed to add correspondence to backend:', response.error);
            }
        } catch (error) {
            console.error('Error adding correspondence to backend:', error);
        }
    }
    
    updateCorrespondenceList() {
        if (!this.correspondenceList) return;
        
        this.correspondenceList.innerHTML = '';
        
        this.correspondences.forEach((corr, index) => {
            const item = Utils.createElement('div', { className: 'correspondence-item' });
            
            const coords = Utils.createElement('div', { className: 'correspondence-coords' });
            coords.innerHTML = `
                <strong>#${index + 1}</strong><br>
                PCD: (${Utils.formatNumber(corr.pointCloudPoint[0])}, 
                      ${Utils.formatNumber(corr.pointCloudPoint[1])}, 
                      ${Utils.formatNumber(corr.pointCloudPoint[2])})<br>
                IMG: (${corr.imagePoint[0]}, ${corr.imagePoint[1]})
            `;
            
            const removeBtn = Utils.createElement('button', {
                className: 'correspondence-remove',
                title: 'Remove correspondence'
            }, '×');
            
            removeBtn.addEventListener('click', () => this.removeCorrespondence(index));
            
            item.appendChild(coords);
            item.appendChild(removeBtn);
            this.correspondenceList.appendChild(item);
        });
    }
    
    removeCorrespondence(index) {
        if (index >= 0 && index < this.correspondences.length) {
            const removed = this.correspondences.splice(index, 1)[0];
            this.updateCorrespondenceList();
            this.updateUI();
            
            Utils.updateStatus(`Removed correspondence #${index + 1}`, 'info');
        }
    }
    
    clearCorrespondences() {
        this.correspondences = [];
        this.pendingPointCloud = null;
        this.pendingImage = null;
        this.calibrationResult = null;
        
        this.updateCorrespondenceList();
        this.updateUI();
        
        // Clear points from viewers
        eventBus.emit('clearAllPoints');
        
        Utils.updateStatus('Cleared all correspondences', 'info');
    }
    
    async computeCalibration() {
        if (!this.pointcloudId || !this.imageId) {
            Utils.updateStatus('Both point cloud and image must be loaded', 'error');
            return;
        }
        
        if (this.correspondences.length < 4) {
            Utils.updateStatus('At least 4 correspondences are required for calibration', 'error');
            return;
        }
        
        Utils.showLoading('Computing calibration...');
        
        try {
            const response = await Utils.makeRequest('/api/calibration/compute', {
                method: 'POST',
                body: JSON.stringify({
                    pointcloud_id: this.pointcloudId,
                    image_id: this.imageId
                })
            });
            
            Utils.hideLoading();
            
            if (response.success) {
                this.calibrationResult = response;
                this.displayCalibrationResults();
                Utils.updateStatus('Calibration computed successfully', 'success');
            } else {
                Utils.updateStatus(`Calibration failed: ${response.error}`, 'error');
            }
        } catch (error) {
            Utils.hideLoading();
            console.error('Error computing calibration:', error);
            Utils.updateStatus('Failed to compute calibration', 'error');
        }
    }
    
    displayCalibrationResults() {
        if (!this.calibrationResult || !this.calibrationResults || !this.calibrationInfo) return;
        
        const result = this.calibrationResult;
        
        let html = '<div class="calibration-metrics">';
        
        // Quality metrics
        html += `
            <div class="calibration-metric">
                <span class="label">Mean Reprojection Error:</span>
                <span class="value">${Utils.formatNumber(result.reprojection_error_mean)} pixels</span>
            </div>
            <div class="calibration-metric">
                <span class="label">Max Reprojection Error:</span>
                <span class="value">${Utils.formatNumber(result.reprojection_error_max)} pixels</span>
            </div>
            <div class="calibration-metric">
                <span class="label">Inlier Points:</span>
                <span class="value">${result.inlier_count} / ${result.total_correspondences}</span>
            </div>
        `;
        
        // Transformation matrix
        html += '<h4>Transformation Matrix:</h4>';
        html += '<div class="matrix-display">';
        const matrix = result.transformation_matrix;
        for (let i = 0; i < 4; i++) {
            html += '<div class="matrix-row">';
            for (let j = 0; j < 4; j++) {
                html += `<span class="matrix-element">${Utils.formatNumber(matrix[i][j])}</span>`;
            }
            html += '</div>';
        }
        html += '</div>';
        
        // Translation vector
        html += '<h4>Translation Vector:</h4>';
        html += `<div class="vector-display">
            X: ${Utils.formatNumber(result.translation_vector[0])}<br>
            Y: ${Utils.formatNumber(result.translation_vector[1])}<br>
            Z: ${Utils.formatNumber(result.translation_vector[2])}
        </div>`;
        
        html += '</div>';
        
        this.calibrationInfo.innerHTML = html;
        this.calibrationResults.style.display = 'block';
    }
    
    async exportCalibration() {
        if (!this.calibrationResult) {
            Utils.updateStatus('No calibration results to export', 'error');
            return;
        }
        
        try {
            const response = await Utils.makeRequest('/api/calibration/export', {
                method: 'POST',
                body: JSON.stringify({
                    calibration_id: this.calibrationResult.calibration_id
                })
            });
            
            if (response.success) {
                // Download the calibration data
                const filename = `calibration_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                Utils.downloadFile(JSON.stringify(response.data, null, 2), filename);
                
                Utils.updateStatus('Calibration exported successfully', 'success');
            } else {
                Utils.updateStatus(`Export failed: ${response.error}`, 'error');
            }
        } catch (error) {
            console.error('Error exporting calibration:', error);
            Utils.updateStatus('Failed to export calibration', 'error');
        }
    }
    
    updateUI() {
        // Update correspondence count
        if (this.correspondenceCount) {
            this.correspondenceCount.textContent = this.correspondences.length;
        }
        
        // Enable/disable compute button
        if (this.computeButton) {
            const canCompute = this.correspondences.length >= 4 && 
                              this.pointcloudId && this.imageId;
            this.computeButton.disabled = !canCompute;
        }
        
        // Enable/disable export button
        if (this.exportButton) {
            this.exportButton.disabled = !this.calibrationResult;
        }
        
        // Enable/disable clear button
        if (this.clearButton) {
            this.clearButton.disabled = this.correspondences.length === 0;
        }
    }
    
    getCorrespondences() {
        return this.correspondences;
    }
    
    getCalibrationResult() {
        return this.calibrationResult;
    }
    
    // Import/Export functionality for correspondences
    exportCorrespondences() {
        const data = {
            correspondences: this.correspondences,
            pointcloud_id: this.pointcloudId,
            image_id: this.imageId,
            timestamp: new Date().toISOString()
        };
        
        const filename = `correspondences_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        Utils.downloadFile(JSON.stringify(data, null, 2), filename);
    }
    
    importCorrespondences(data) {
        try {
            this.correspondences = data.correspondences || [];
            this.pointcloudId = data.pointcloud_id;
            this.imageId = data.image_id;
            
            this.updateCorrespondenceList();
            this.updateUI();
            
            Utils.updateStatus(`Imported ${this.correspondences.length} correspondences`, 'success');
            return true;
        } catch (error) {
            console.error('Error importing correspondences:', error);
            Utils.updateStatus('Failed to import correspondences', 'error');
            return false;
        }
    }
}

// Export for global use
window.CalibrationManager = CalibrationManager;
