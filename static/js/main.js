/**
 * Main application controller
 */

class CalibrationApp {
    constructor() {
        // Initialize components
        this.pointCloudViewer = null;
        this.imageViewer = null;
        this.calibrationManager = null;
        
        // UI elements
        this.pcdFileInput = document.getElementById('pcdFile');
        this.imageFileInput = document.getElementById('imageFile');
        this.pointSizeSlider = document.getElementById('pointSize');
        this.pointSizeValue = document.getElementById('pointSizeValue');
        this.showIntensityCheckbox = document.getElementById('showIntensity');
        this.showAxesCheckbox = document.getElementById('showAxes');
        this.modeRadios = document.querySelectorAll('input[name="mode"]');
        this.resetViewButton = document.getElementById('resetView');
        
        // Current data
        this.currentPointCloudData = null;
        this.currentImageData = null;
        
        this.init();
    }
    
    init() {
        // Initialize viewers
        this.pointCloudViewer = new PointCloudViewer('threejs-container');
        this.imageViewer = new ImageViewer('imageCanvas');
        this.calibrationManager = new CalibrationManager();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Initial UI state
        this.updateSelectionMode();
        
        Utils.updateStatus('Application initialized. Load point cloud and image files to begin.', 'info');
    }
    
    setupEventListeners() {
        // File upload handlers
        if (this.pcdFileInput) {
            this.pcdFileInput.addEventListener('change', (e) => this.handlePCDUpload(e));
        }
        
        if (this.imageFileInput) {
            this.imageFileInput.addEventListener('change', (e) => this.handleImageUpload(e));
        }
        
        // Control handlers
        if (this.pointSizeSlider) {
            this.pointSizeSlider.addEventListener('input', (e) => {
                const size = parseInt(e.target.value);
                this.pointSizeValue.textContent = size;
                eventBus.emit('pointSizeChanged', size);
            });
        }
        
        if (this.showIntensityCheckbox) {
            this.showIntensityCheckbox.addEventListener('change', (e) => {
                eventBus.emit('intensityToggled', e.target.checked);
            });
        }
        
        if (this.showAxesCheckbox) {
            this.showAxesCheckbox.addEventListener('change', (e) => {
                eventBus.emit('axesToggled', e.target.checked);
            });
        }
        
        // Mode selection
        this.modeRadios.forEach(radio => {
            radio.addEventListener('change', () => this.updateSelectionMode());
        });
        
        // Reset view button
        if (this.resetViewButton) {
            this.resetViewButton.addEventListener('click', () => this.resetViews());
        }
        
        // Global event listeners
        eventBus.on('clearAllPoints', () => {
            if (this.imageViewer) {
                this.imageViewer.clearPoints();
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    }
    
    updateSelectionMode() {
        const selectedMode = document.querySelector('input[name="mode"]:checked');
        const isSelectionMode = selectedMode && selectedMode.value === 'select';
        
        eventBus.emit('selectionModeChanged', isSelectionMode);
        
        Utils.updateStatus(
            isSelectionMode ? 
            'Selection mode: Click points to create correspondences' : 
            'View mode: Pan and zoom to explore data',
            'info'
        );
    }
    
    async handlePCDUpload(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        // Validate file type
        if (!Utils.validateFileType(file, ['pcd'])) {
            Utils.showFileStatus('pcdStatus', 'Invalid file type. Please select a PCD file.', 'error');
            return;
        }
        
        Utils.showLoading('Uploading and processing PCD file...');
        Utils.showFileStatus('pcdStatus', `Uploading ${file.name} (${Utils.formatFileSize(file.size)})...`, 'info');
        
        try {
            const response = await Utils.uploadFile('/api/upload/pcd', file, (progress) => {
                Utils.showFileStatus('pcdStatus', `Uploading... ${Math.round(progress)}%`, 'info');
            });
            
            if (response.success) {
                // Load point cloud data
                await this.loadPointCloudData(response.data_id);
                
                Utils.showFileStatus('pcdStatus', 
                    `Loaded: ${response.filename} (${response.point_count} points)`, 'success');
                
                // Emit event for calibration manager
                eventBus.emit('pointcloudLoaded', response);
                
            } else {
                Utils.showFileStatus('pcdStatus', `Error: ${response.error}`, 'error');
            }
        } catch (error) {
            console.error('PCD upload error:', error);
            Utils.showFileStatus('pcdStatus', 'Upload failed. Please try again.', 'error');
        } finally {
            Utils.hideLoading();
        }
    }
    
    async handleImageUpload(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        // Validate file type
        const allowedTypes = ['jpg', 'jpeg', 'png', 'bmp', 'tiff', 'tif'];
        if (!Utils.validateFileType(file, allowedTypes)) {
            Utils.showFileStatus('imageStatus', 'Invalid file type. Please select an image file.', 'error');
            return;
        }
        
        Utils.showLoading('Uploading and processing image file...');
        Utils.showFileStatus('imageStatus', `Uploading ${file.name} (${Utils.formatFileSize(file.size)})...`, 'info');
        
        try {
            const response = await Utils.uploadFile('/api/upload/image', file, (progress) => {
                Utils.showFileStatus('imageStatus', `Uploading... ${Math.round(progress)}%`, 'info');
            });
            
            if (response.success) {
                // Load image
                await this.loadImageData(response.data_id);
                
                Utils.showFileStatus('imageStatus', 
                    `Loaded: ${response.filename} (${response.dimensions[0]}×${response.dimensions[1]})`, 'success');
                
                // Emit event for calibration manager
                eventBus.emit('imageLoaded', response);
                
            } else {
                Utils.showFileStatus('imageStatus', `Error: ${response.error}`, 'error');
            }
        } catch (error) {
            console.error('Image upload error:', error);
            Utils.showFileStatus('imageStatus', 'Upload failed. Please try again.', 'error');
        } finally {
            Utils.hideLoading();
        }
    }
    
    async loadPointCloudData(dataId) {
        try {
            Utils.showLoading('Loading point cloud data...');
            
            const response = await Utils.makeRequest(`/api/pointcloud/${dataId}`);
            
            if (response.success) {
                this.currentPointCloudData = response;
                
                // Load into viewer
                const success = this.pointCloudViewer.loadPointCloud(response);
                
                if (success) {
                    Utils.updateStatus('Point cloud loaded successfully', 'success');
                } else {
                    Utils.updateStatus('Failed to display point cloud', 'error');
                }
            } else {
                Utils.updateStatus(`Failed to load point cloud: ${response.error}`, 'error');
            }
        } catch (error) {
            console.error('Error loading point cloud data:', error);
            Utils.updateStatus('Failed to load point cloud data', 'error');
        } finally {
            Utils.hideLoading();
        }
    }
    
    async loadImageData(dataId) {
        try {
            Utils.showLoading('Loading image...');
            
            // Load image directly from processed folder
            const imageUrl = `/api/image/${dataId}`;
            const success = await this.imageViewer.loadImage(imageUrl);
            
            if (success) {
                this.currentImageData = { data_id: dataId };
                Utils.updateStatus('Image loaded successfully', 'success');
            } else {
                Utils.updateStatus('Failed to load image', 'error');
            }
        } catch (error) {
            console.error('Error loading image:', error);
            Utils.updateStatus('Failed to load image', 'error');
        } finally {
            Utils.hideLoading();
        }
    }
    
    resetViews() {
        if (this.pointCloudViewer) {
            this.pointCloudViewer.resetView();
        }
        
        if (this.imageViewer) {
            this.imageViewer.resetView();
        }
        
        Utils.updateStatus('Views reset to default', 'info');
    }
    
    handleKeyboard(event) {
        // Handle keyboard shortcuts
        if (event.ctrlKey) {
            switch (event.key.toLowerCase()) {
                case 'r':
                    event.preventDefault();
                    this.resetViews();
                    break;
                    
                case 's':
                    event.preventDefault();
                    if (this.calibrationManager && this.calibrationManager.calibrationResult) {
                        this.calibrationManager.exportCalibration();
                    }
                    break;
                    
                case 'c':
                    event.preventDefault();
                    if (this.calibrationManager) {
                        this.calibrationManager.clearCorrespondences();
                    }
                    break;
            }
        }
        
        // Toggle modes with spacebar
        if (event.code === 'Space' && !event.target.matches('input, textarea, button')) {
            event.preventDefault();
            const currentMode = document.querySelector('input[name="mode"]:checked');
            if (currentMode) {
                const newMode = currentMode.value === 'select' ? 'view' : 'select';
                const newRadio = document.querySelector(`input[name="mode"][value="${newMode}"]`);
                if (newRadio) {
                    newRadio.checked = true;
                    this.updateSelectionMode();
                }
            }
        }
    }
    
    // Utility methods for external access
    getCurrentPointCloudData() {
        return this.currentPointCloudData;
    }
    
    getCurrentImageData() {
        return this.currentImageData;
    }
    
    getCalibrationManager() {
        return this.calibrationManager;
    }
    
    getPointCloudViewer() {
        return this.pointCloudViewer;
    }
    
    getImageViewer() {
        return this.imageViewer;
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.calibrationApp = new CalibrationApp();
    
    // Add some helpful console messages
    console.log('Sensor Calibration Tool initialized');
    console.log('Keyboard shortcuts:');
    console.log('  Ctrl+R: Reset views');
    console.log('  Ctrl+S: Export calibration');
    console.log('  Ctrl+C: Clear correspondences');
    console.log('  Space: Toggle selection/view mode');
});
