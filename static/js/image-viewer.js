/**
 * Image Viewer with point selection capabilities
 */

class ImageViewer {
    constructor(canvasId) {
        this.canvasId = canvasId;
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        
        // Image data
        this.image = null;
        this.imageData = null;
        this.originalImageData = null;
        
        // View state
        this.scale = 1;
        this.offsetX = 0;
        this.offsetY = 0;
        this.isDragging = false;
        this.lastMouseX = 0;
        this.lastMouseY = 0;
        
        // Selection
        this.selectionMode = false;
        this.selectedPoints = [];
        this.pointRadius = 5;
        
        // Settings
        this.showPoints = true;
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Mouse events
        this.canvas.addEventListener('mousedown', (e) => this.onMouseDown(e));
        this.canvas.addEventListener('mousemove', (e) => this.onMouseMove(e));
        this.canvas.addEventListener('mouseup', (e) => this.onMouseUp(e));
        this.canvas.addEventListener('wheel', (e) => this.onWheel(e));
        this.canvas.addEventListener('click', (e) => this.onClick(e));
        
        // Context menu (right-click)
        this.canvas.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.onRightClick(e);
        });
        
        // Listen for mode changes
        eventBus.on('selectionModeChanged', (enabled) => {
            this.selectionMode = enabled;
            this.canvas.style.cursor = enabled ? 'crosshair' : 'default';
        });
        
        // Listen for point removal
        eventBus.on('removeImagePoint', (index) => {
            this.removePoint(index);
        });
        
        // Prevent default drag behavior
        this.canvas.addEventListener('dragstart', (e) => e.preventDefault());
    }
    
    loadImage(imageSrc) {
        return new Promise((resolve, reject) => {
            this.image = new Image();
            
            this.image.onload = () => {
                // Set canvas size to fit image while maintaining aspect ratio
                this.fitImageToCanvas();
                
                // Draw image
                this.draw();
                
                Utils.updateStatus(`Loaded image: ${this.image.width}x${this.image.height}`, 'success');
                resolve(true);
            };
            
            this.image.onerror = () => {
                Utils.updateStatus('Failed to load image', 'error');
                reject(false);
            };
            
            this.image.src = imageSrc;
        });
    }
    
    fitImageToCanvas() {
        if (!this.image) return;
        
        const containerWidth = this.canvas.parentElement.clientWidth;
        const containerHeight = this.canvas.parentElement.clientHeight;
        
        // Calculate scale to fit image in container
        const scaleX = containerWidth / this.image.width;
        const scaleY = containerHeight / this.image.height;
        this.scale = Math.min(scaleX, scaleY, 1); // Don't scale up beyond original size
        
        // Set canvas size
        this.canvas.width = this.image.width * this.scale;
        this.canvas.height = this.image.height * this.scale;
        
        // Center the image
        this.offsetX = 0;
        this.offsetY = 0;
    }
    
    draw() {
        if (!this.image) return;
        
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Save context state
        this.ctx.save();
        
        // Apply transformations
        this.ctx.translate(this.offsetX, this.offsetY);
        this.ctx.scale(this.scale, this.scale);
        
        // Draw image
        this.ctx.drawImage(this.image, 0, 0);
        
        // Restore context state
        this.ctx.restore();
        
        // Draw selected points
        if (this.showPoints) {
            this.drawPoints();
        }
    }
    
    drawPoints() {
        this.ctx.save();
        
        this.selectedPoints.forEach((point, index) => {
            const screenX = point.x * this.scale + this.offsetX;
            const screenY = point.y * this.scale + this.offsetY;
            
            // Draw point circle
            this.ctx.beginPath();
            this.ctx.arc(screenX, screenY, this.pointRadius, 0, 2 * Math.PI);
            this.ctx.fillStyle = 'rgba(255, 0, 0, 0.7)';
            this.ctx.fill();
            this.ctx.strokeStyle = 'white';
            this.ctx.lineWidth = 2;
            this.ctx.stroke();
            
            // Draw point number
            this.ctx.fillStyle = 'white';
            this.ctx.font = '12px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText((index + 1).toString(), screenX, screenY + 4);
        });
        
        this.ctx.restore();
    }
    
    onMouseDown(event) {
        const rect = this.canvas.getBoundingClientRect();
        this.lastMouseX = event.clientX - rect.left;
        this.lastMouseY = event.clientY - rect.top;
        
        if (!this.selectionMode) {
            this.isDragging = true;
            this.canvas.style.cursor = 'grabbing';
        }
    }
    
    onMouseMove(event) {
        const rect = this.canvas.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;
        
        if (this.isDragging && !this.selectionMode) {
            // Pan the image
            const deltaX = mouseX - this.lastMouseX;
            const deltaY = mouseY - this.lastMouseY;
            
            this.offsetX += deltaX;
            this.offsetY += deltaY;
            
            this.draw();
        }
        
        // Update mouse position info
        if (this.image) {
            const imageX = (mouseX - this.offsetX) / this.scale;
            const imageY = (mouseY - this.offsetY) / this.scale;
            
            if (imageX >= 0 && imageX < this.image.width && 
                imageY >= 0 && imageY < this.image.height) {
                this.updateImageInfo(imageX, imageY);
            } else {
                this.clearImageInfo();
            }
        }
        
        this.lastMouseX = mouseX;
        this.lastMouseY = mouseY;
    }
    
    onMouseUp(event) {
        this.isDragging = false;
        this.canvas.style.cursor = this.selectionMode ? 'crosshair' : 'default';
    }
    
    onWheel(event) {
        event.preventDefault();
        
        const rect = this.canvas.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;
        
        // Calculate zoom
        const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;
        const newScale = Utils.clamp(this.scale * zoomFactor, 0.1, 5);
        
        // Zoom towards mouse position
        const imageX = (mouseX - this.offsetX) / this.scale;
        const imageY = (mouseY - this.offsetY) / this.scale;
        
        this.scale = newScale;
        
        this.offsetX = mouseX - imageX * this.scale;
        this.offsetY = mouseY - imageY * this.scale;
        
        this.draw();
    }
    
    onClick(event) {
        if (!this.selectionMode || !this.image) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;
        
        // Convert to image coordinates
        const imageX = (mouseX - this.offsetX) / this.scale;
        const imageY = (mouseY - this.offsetY) / this.scale;
        
        // Check if click is within image bounds
        if (imageX >= 0 && imageX < this.image.width && 
            imageY >= 0 && imageY < this.image.height) {
            
            // Check if clicking near existing point (for removal)
            const clickedPointIndex = this.findNearestPoint(mouseX, mouseY);
            
            if (clickedPointIndex !== -1 && event.ctrlKey) {
                // Remove point if Ctrl+click
                this.removePoint(clickedPointIndex);
            } else {
                // Add new point
                this.addPoint(imageX, imageY);
            }
        }
    }
    
    onRightClick(event) {
        if (!this.selectionMode) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;
        
        // Find nearest point for removal
        const pointIndex = this.findNearestPoint(mouseX, mouseY);
        
        if (pointIndex !== -1) {
            this.removePoint(pointIndex);
        }
    }
    
    addPoint(x, y) {
        const point = { x: Math.round(x), y: Math.round(y) };
        this.selectedPoints.push(point);
        
        // Emit point selection event
        eventBus.emit('imagePointSelected', {
            point: [point.x, point.y],
            index: this.selectedPoints.length - 1
        });
        
        this.draw();
        
        Utils.updateStatus(`Added image point at (${point.x}, ${point.y})`, 'info');
    }
    
    removePoint(index) {
        if (index >= 0 && index < this.selectedPoints.length) {
            const removedPoint = this.selectedPoints.splice(index, 1)[0];
            
            // Emit point removal event
            eventBus.emit('imagePointRemoved', {
                point: [removedPoint.x, removedPoint.y],
                index: index
            });
            
            this.draw();
            
            Utils.updateStatus(`Removed image point at (${removedPoint.x}, ${removedPoint.y})`, 'info');
        }
    }
    
    findNearestPoint(screenX, screenY) {
        let nearestIndex = -1;
        let minDistance = this.pointRadius * 2; // Click tolerance
        
        this.selectedPoints.forEach((point, index) => {
            const pointScreenX = point.x * this.scale + this.offsetX;
            const pointScreenY = point.y * this.scale + this.offsetY;
            
            const distance = Utils.distance2D([screenX, screenY], [pointScreenX, pointScreenY]);
            
            if (distance < minDistance) {
                minDistance = distance;
                nearestIndex = index;
            }
        });
        
        return nearestIndex;
    }
    
    updateImageInfo(x, y) {
        const infoElement = document.getElementById('imageInfo');
        if (!infoElement) return;
        
        let info = `Image Coordinates\n`;
        info += `X: ${Math.round(x)}\n`;
        info += `Y: ${Math.round(y)}\n`;
        info += `Scale: ${Utils.formatNumber(this.scale, 2)}x`;
        
        infoElement.textContent = info;
        infoElement.style.display = 'block';
    }
    
    clearImageInfo() {
        const infoElement = document.getElementById('imageInfo');
        if (infoElement) {
            infoElement.style.display = 'none';
        }
    }
    
    resetView() {
        if (this.image) {
            this.fitImageToCanvas();
            this.draw();
        }
    }
    
    clearPoints() {
        this.selectedPoints = [];
        this.draw();
        
        eventBus.emit('imagePointsCleared');
        Utils.updateStatus('Cleared all image points', 'info');
    }
    
    getSelectedPoints() {
        return this.selectedPoints.map(p => [p.x, p.y]);
    }
    
    setPointVisibility(visible) {
        this.showPoints = visible;
        this.draw();
    }
    
    exportPoints() {
        const points = this.getSelectedPoints();
        const data = {
            image_points: points,
            image_dimensions: this.image ? [this.image.width, this.image.height] : null,
            timestamp: new Date().toISOString()
        };
        
        Utils.downloadFile(JSON.stringify(data, null, 2), 'image_points.json');
    }
    
    importPoints(pointsData) {
        try {
            this.selectedPoints = pointsData.map(p => ({ x: p[0], y: p[1] }));
            this.draw();
            
            Utils.updateStatus(`Imported ${this.selectedPoints.length} image points`, 'success');
            return true;
        } catch (error) {
            console.error('Error importing points:', error);
            Utils.updateStatus('Failed to import image points', 'error');
            return false;
        }
    }
}

// Export for global use
window.ImageViewer = ImageViewer;
