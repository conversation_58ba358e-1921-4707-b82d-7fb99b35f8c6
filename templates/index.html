<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sensor Calibration Tool</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/controls/OrbitControls.js"></script>
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="header">
            <h1>Sensor Calibration Tool</h1>
            <div class="header-controls">
                <button id="resetView" class="btn btn-secondary">Reset View</button>
                <button id="exportCalibration" class="btn btn-primary" disabled>Export Calibration</button>
            </div>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Left Panel - File Upload and Controls -->
            <div class="left-panel">
                <div class="panel-section">
                    <h3>File Upload</h3>
                    
                    <!-- Point Cloud Upload -->
                    <div class="upload-section">
                        <label for="pcdFile">Point Cloud (PCD):</label>
                        <input type="file" id="pcdFile" accept=".pcd" />
                        <div id="pcdStatus" class="status-info"></div>
                    </div>
                    
                    <!-- Image Upload -->
                    <div class="upload-section">
                        <label for="imageFile">Image:</label>
                        <input type="file" id="imageFile" accept=".jpg,.jpeg,.png,.bmp,.tiff,.tif" />
                        <div id="imageStatus" class="status-info"></div>
                    </div>
                </div>

                <!-- Visualization Controls -->
                <div class="panel-section">
                    <h3>Visualization Controls</h3>
                    
                    <div class="control-group">
                        <label>Point Size:</label>
                        <input type="range" id="pointSize" min="1" max="10" value="2" />
                        <span id="pointSizeValue">2</span>
                    </div>
                    
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="showIntensity" checked />
                            Show Intensity Colors
                        </label>
                    </div>
                    
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="showAxes" checked />
                            Show Coordinate Axes
                        </label>
                    </div>
                </div>

                <!-- Calibration Controls -->
                <div class="panel-section">
                    <h3>Calibration</h3>
                    
                    <div class="calibration-mode">
                        <label>
                            <input type="radio" name="mode" value="select" checked />
                            Point Selection Mode
                        </label>
                        <label>
                            <input type="radio" name="mode" value="view" />
                            View Mode
                        </label>
                    </div>
                    
                    <div class="correspondence-info">
                        <p>Correspondences: <span id="correspondenceCount">0</span></p>
                        <button id="clearCorrespondences" class="btn btn-warning">Clear All</button>
                        <button id="computeCalibration" class="btn btn-success" disabled>Compute Calibration</button>
                    </div>
                </div>

                <!-- Correspondence List -->
                <div class="panel-section">
                    <h3>Correspondence Points</h3>
                    <div id="correspondenceList" class="correspondence-list">
                        <!-- Correspondence points will be listed here -->
                    </div>
                </div>

                <!-- Calibration Results -->
                <div class="panel-section" id="calibrationResults" style="display: none;">
                    <h3>Calibration Results</h3>
                    <div id="calibrationInfo">
                        <!-- Calibration results will be displayed here -->
                    </div>
                </div>
            </div>

            <!-- Center Panel - 3D Visualization -->
            <div class="center-panel">
                <div class="visualization-container">
                    <div id="threejs-container" class="threejs-container">
                        <!-- Three.js canvas will be inserted here -->
                    </div>
                    <div class="visualization-info">
                        <div id="pointInfo" class="info-overlay">
                            <!-- Point information will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Image Display -->
            <div class="right-panel">
                <div class="image-container">
                    <canvas id="imageCanvas" class="image-canvas"></canvas>
                    <div class="image-info">
                        <div id="imageInfo" class="info-overlay">
                            <!-- Image information will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div id="statusMessage">Ready</div>
            <div id="loadingIndicator" class="loading-indicator" style="display: none;">
                <div class="spinner"></div>
                <span>Processing...</span>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/pointcloud-viewer.js') }}"></script>
    <script src="{{ url_for('static', filename='js/image-viewer.js') }}"></script>
    <script src="{{ url_for('static', filename='js/calibration-manager.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
