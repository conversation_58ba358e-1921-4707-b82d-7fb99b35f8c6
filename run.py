#!/usr/bin/env python3
"""
Startup script for the sensor calibration tool
"""

import os
import sys
import argparse
from pathlib import Path

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'flask',
        'numpy',
        'open3d',
        'opencv-python',
        'pillow',
        'scipy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'opencv-python':
                import cv2
            elif package == 'pillow':
                import PIL
            else:
                __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nPlease install missing packages using:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All required dependencies are installed")
    return True

def setup_directories():
    """Create necessary directories"""
    directories = [
        'uploads',
        'processed', 
        'data',
        'data/pointclouds',
        'data/images',
        'data/calibrations'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Directory structure created")

def run_tests():
    """Run the test suite"""
    print("Running test suite...")
    try:
        import test_app
        return test_app.main() == 0
    except Exception as e:
        print(f"❌ Test suite failed: {str(e)}")
        return False

def start_server(host='0.0.0.0', port=5000, debug=False):
    """Start the Flask development server"""
    try:
        from app import app
        print(f"🚀 Starting server on http://{host}:{port}")
        print("Press Ctrl+C to stop the server")
        app.run(host=host, port=port, debug=debug)
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"❌ Failed to start server: {str(e)}")
        return False
    
    return True

def main():
    parser = argparse.ArgumentParser(description='Sensor Calibration Tool')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000, help='Port to bind to (default: 5000)')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--test', action='store_true', help='Run tests only')
    parser.add_argument('--skip-deps-check', action='store_true', help='Skip dependency check')
    
    args = parser.parse_args()
    
    print("🔧 Sensor Calibration Tool")
    print("=" * 40)
    
    # Check dependencies
    if not args.skip_deps_check:
        if not check_dependencies():
            return 1
    
    # Setup directories
    setup_directories()
    
    # Run tests if requested
    if args.test:
        if run_tests():
            print("✅ All tests passed!")
            return 0
        else:
            print("❌ Some tests failed!")
            return 1
    
    # Start server
    print("\n🌐 Web Interface:")
    print(f"   Local:    http://localhost:{args.port}")
    print(f"   Network:  http://{args.host}:{args.port}")
    print("\n📖 Usage:")
    print("   1. Open the web interface in your browser")
    print("   2. Upload a PCD file and an image file")
    print("   3. Switch to 'Point Selection Mode'")
    print("   4. Click corresponding points in both views")
    print("   5. Click 'Compute Calibration' when you have 4+ correspondences")
    print("   6. Export the calibration results")
    print()
    
    return 0 if start_server(args.host, args.port, args.debug) else 1

if __name__ == "__main__":
    sys.exit(main())
