#!/usr/bin/env python3
"""
Generate sample data for testing the sensor calibration tool
Creates synthetic PCD and image files with known correspondences
"""

import numpy as np
import os
from pathlib import Path

def generate_sample_pcd(filename="sample_pointcloud.pcd", num_points=1000):
    """Generate a sample PCD file with intensity data"""
    
    # Generate points in a reasonable 3D space (like a street scene)
    points = []
    
    # Ground plane points
    for i in range(num_points // 3):
        x = np.random.uniform(-20, 20)
        y = np.random.uniform(-20, 20)
        z = np.random.uniform(-0.5, 0.5)  # Ground level
        intensity = np.random.uniform(50, 150)
        points.append([x, y, z, intensity])
    
    # Building/object points
    for i in range(num_points // 3):
        x = np.random.uniform(-15, 15)
        y = np.random.uniform(-15, 15)
        z = np.random.uniform(0, 8)  # Building height
        intensity = np.random.uniform(100, 255)
        points.append([x, y, z, intensity])
    
    # Vegetation/noise points
    for i in range(num_points - 2 * (num_points // 3)):
        x = np.random.uniform(-25, 25)
        y = np.random.uniform(-25, 25)
        z = np.random.uniform(-1, 10)
        intensity = np.random.uniform(20, 100)
        points.append([x, y, z, intensity])
    
    # Create PCD content
    pcd_content = f"""# .PCD v0.7 - Point Cloud Data file format
VERSION 0.7
FIELDS x y z intensity
SIZE 4 4 4 4
TYPE F F F F
COUNT 1 1 1 1
WIDTH {len(points)}
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS {len(points)}
DATA ascii
"""
    
    # Add point data
    for point in points:
        pcd_content += f"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f} {point[3]:.1f}\n"
    
    # Write to file
    with open(filename, 'w') as f:
        f.write(pcd_content)
    
    print(f"Generated sample PCD file: {filename}")
    print(f"  - Points: {len(points)}")
    print(f"  - Size: {os.path.getsize(filename) / 1024:.1f} KB")
    
    return points

def generate_sample_image(filename="sample_image.png", width=800, height=600):
    """Generate a sample image that could correspond to the point cloud"""
    
    try:
        from PIL import Image, ImageDraw
        import numpy as np
        
        # Create base image
        image = Image.new('RGB', (width, height), color='lightblue')
        draw = ImageDraw.Draw(image)
        
        # Draw ground (bottom half)
        ground_color = (100, 150, 100)  # Green-ish
        draw.rectangle([0, height//2, width, height], fill=ground_color)
        
        # Draw some buildings/objects
        building_color = (120, 120, 120)  # Gray
        
        # Building 1
        draw.rectangle([100, 200, 200, height//2], fill=building_color)
        
        # Building 2
        draw.rectangle([300, 150, 450, height//2], fill=building_color)
        
        # Building 3
        draw.rectangle([600, 180, 700, height//2], fill=building_color)
        
        # Add some texture/noise
        pixels = np.array(image)
        noise = np.random.randint(-20, 20, pixels.shape, dtype=np.int16)
        pixels = np.clip(pixels.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        image = Image.fromarray(pixels)
        
        # Add some distinctive features for correspondence selection
        draw = ImageDraw.Draw(image)
        
        # Red marker points
        marker_color = (255, 0, 0)
        marker_positions = [
            (150, 250),  # On building 1
            (375, 200),  # On building 2
            (650, 230),  # On building 3
            (400, height//2 - 10),  # On ground
            (200, height//2 - 5),   # On ground
        ]
        
        for pos in marker_positions:
            # Draw a small circle
            draw.ellipse([pos[0]-5, pos[1]-5, pos[0]+5, pos[1]+5], fill=marker_color)
        
        # Save image
        image.save(filename, 'PNG')
        
        print(f"Generated sample image: {filename}")
        print(f"  - Dimensions: {width}x{height}")
        print(f"  - Size: {os.path.getsize(filename) / 1024:.1f} KB")
        print(f"  - Marker points: {len(marker_positions)}")
        
        return marker_positions
        
    except ImportError:
        print("PIL not available. Creating a simple placeholder image description.")
        
        # Create a simple text file describing the image
        description = f"""Sample Image Description
Width: {width}
Height: {height}
Content: Synthetic scene with buildings and ground plane
Marker points for correspondence:
- (150, 250) - Building 1
- (375, 200) - Building 2  
- (650, 230) - Building 3
- (400, {height//2 - 10}) - Ground
- (200, {height//2 - 5}) - Ground

Note: PIL not available, so actual image file was not created.
"""
        
        with open(filename.replace('.png', '_description.txt'), 'w') as f:
            f.write(description)
        
        return []

def generate_correspondence_hints(pcd_points, image_markers):
    """Generate hints for correspondence selection"""
    
    if not image_markers:
        print("No image markers available for correspondence hints")
        return
    
    print("\n" + "="*50)
    print("CORRESPONDENCE SELECTION HINTS")
    print("="*50)
    print("For testing the calibration tool, try selecting these correspondences:")
    print()
    
    # Suggest some 3D points that might correspond to image markers
    suggested_3d_points = [
        (5.0, 8.0, 2.5),   # Building-like point
        (0.0, 12.0, 3.0),  # Building-like point
        (-8.0, 10.0, 2.8), # Building-like point
        (2.0, 15.0, 0.1),  # Ground point
        (-3.0, 12.0, 0.0), # Ground point
    ]
    
    for i, (pcd_point, img_point) in enumerate(zip(suggested_3d_points, image_markers)):
        print(f"Correspondence {i+1}:")
        print(f"  3D Point: ({pcd_point[0]:.1f}, {pcd_point[1]:.1f}, {pcd_point[2]:.1f})")
        print(f"  2D Point: ({img_point[0]}, {img_point[1]})")
        print()
    
    print("Note: These are approximate suggestions. In the actual tool,")
    print("you should select points that visually correspond between")
    print("the 3D point cloud and 2D image views.")

def main():
    """Generate sample data files"""
    
    print("Generating Sample Data for Sensor Calibration Tool")
    print("="*50)
    
    # Create sample directory
    sample_dir = Path("sample_data")
    sample_dir.mkdir(exist_ok=True)
    
    # Generate PCD file
    pcd_file = sample_dir / "sample_pointcloud.pcd"
    pcd_points = generate_sample_pcd(str(pcd_file))
    
    print()
    
    # Generate image file
    img_file = sample_dir / "sample_image.png"
    image_markers = generate_sample_image(str(img_file))
    
    print()
    
    # Generate correspondence hints
    generate_correspondence_hints(pcd_points, image_markers)
    
    print("\n" + "="*50)
    print("USAGE INSTRUCTIONS")
    print("="*50)
    print("1. Start the calibration tool:")
    print("   python run.py")
    print()
    print("2. Open http://localhost:5000 in your browser")
    print()
    print("3. Upload the generated files:")
    print(f"   - Point Cloud: {pcd_file}")
    print(f"   - Image: {img_file}")
    print()
    print("4. Switch to 'Point Selection Mode'")
    print()
    print("5. Select corresponding points using the hints above")
    print()
    print("6. Click 'Compute Calibration' when you have 4+ correspondences")
    print()
    print("7. Export the results")

if __name__ == "__main__":
    main()
