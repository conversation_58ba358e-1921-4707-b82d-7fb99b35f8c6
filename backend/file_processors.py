"""
File processing modules for handling PCD and image files
"""

import numpy as np
import open3d as o3d
from PIL import Image
import cv2
import os
import logging
from typing import Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)

class PCDProcessor:
    """Handles PCD file loading and processing for visualization"""
    
    def __init__(self):
        self.supported_formats = ['.pcd']
    
    def process_file(self, filepath: str) -> Dict[str, Any]:
        """
        Process a PCD file and extract point cloud data
        
        Args:
            filepath: Path to the PCD file
            
        Returns:
            Dictionary containing processed data or error information
        """
        try:
            # Load point cloud using Open3D
            pcd = o3d.io.read_point_cloud(filepath)
            
            if len(pcd.points) == 0:
                return {
                    'success': False,
                    'error': 'Empty point cloud or invalid PCD file'
                }
            
            # Extract points as numpy array
            points = np.asarray(pcd.points)
            
            # Check for colors
            has_colors = len(pcd.colors) > 0
            colors = np.asarray(pcd.colors) if has_colors else None
            
            # Check for normals
            has_normals = len(pcd.normals) > 0
            normals = np.asarray(pcd.normals) if has_normals else None
            
            # Try to extract intensity information if available
            # Note: Open3D doesn't directly support intensity, but we can check if it's in the original file
            intensities = self._extract_intensity_from_pcd(filepath)
            has_intensity = intensities is not None
            
            # Calculate bounding box
            bounds = {
                'min': points.min(axis=0).tolist(),
                'max': points.max(axis=0).tolist(),
                'center': points.mean(axis=0).tolist()
            }
            
            # Generate colors from intensity if available and no colors exist
            if has_intensity and not has_colors:
                colors = self._intensity_to_colors(intensities)
                has_colors = True
            
            # If no colors or intensity, generate default colors
            if not has_colors:
                colors = np.ones((len(points), 3)) * 0.7  # Default gray color
            
            processed_data = {
                'points': points,
                'colors': colors,
                'normals': normals,
                'intensities': intensities,
                'has_colors': has_colors,
                'has_normals': has_normals,
                'has_intensity': has_intensity,
                'bounds': bounds,
                'point_count': len(points)
            }
            
            return {
                'success': True,
                'data': processed_data,
                'point_count': len(points),
                'bounds': bounds,
                'has_intensity': has_intensity
            }
            
        except Exception as e:
            logger.error(f"Error processing PCD file {filepath}: {str(e)}")
            return {
                'success': False,
                'error': f'Failed to process PCD file: {str(e)}'
            }
    
    def _extract_intensity_from_pcd(self, filepath: str) -> Optional[np.ndarray]:
        """
        Extract intensity values from PCD file by parsing the file directly
        
        Args:
            filepath: Path to the PCD file
            
        Returns:
            Numpy array of intensity values or None if not available
        """
        try:
            with open(filepath, 'r') as f:
                lines = f.readlines()
            
            # Find the FIELDS line to check if intensity is present
            fields_line = None
            data_start = 0
            
            for i, line in enumerate(lines):
                if line.startswith('FIELDS'):
                    fields_line = line.strip()
                elif line.startswith('DATA'):
                    data_start = i + 1
                    break
            
            if fields_line is None:
                return None
            
            # Parse fields
            fields = fields_line.split()[1:]  # Skip 'FIELDS' keyword
            
            if 'intensity' not in fields and 'i' not in fields:
                return None
            
            # Find intensity column index
            intensity_idx = None
            for idx, field in enumerate(fields):
                if field.lower() in ['intensity', 'i']:
                    intensity_idx = idx
                    break
            
            if intensity_idx is None:
                return None
            
            # Extract intensity values
            intensities = []
            for line in lines[data_start:]:
                if line.strip():
                    values = line.strip().split()
                    if len(values) > intensity_idx:
                        try:
                            intensities.append(float(values[intensity_idx]))
                        except ValueError:
                            continue
            
            return np.array(intensities) if intensities else None
            
        except Exception as e:
            logger.warning(f"Could not extract intensity from {filepath}: {str(e)}")
            return None
    
    def _intensity_to_colors(self, intensities: np.ndarray) -> np.ndarray:
        """
        Convert intensity values to RGB colors using a colormap
        
        Args:
            intensities: Array of intensity values
            
        Returns:
            RGB color array
        """
        # Normalize intensities to 0-1 range
        if intensities.max() > intensities.min():
            normalized = (intensities - intensities.min()) / (intensities.max() - intensities.min())
        else:
            normalized = np.ones_like(intensities) * 0.5
        
        # Apply colormap (jet colormap)
        colors = np.zeros((len(intensities), 3))
        
        # Simple jet colormap implementation
        for i, val in enumerate(normalized):
            if val < 0.25:
                colors[i] = [0, 4 * val, 1]
            elif val < 0.5:
                colors[i] = [0, 1, 1 - 4 * (val - 0.25)]
            elif val < 0.75:
                colors[i] = [4 * (val - 0.5), 1, 0]
            else:
                colors[i] = [1, 1 - 4 * (val - 0.75), 0]
        
        return colors


class ImageProcessor:
    """Handles image file loading and processing"""
    
    def __init__(self):
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
    
    def process_file(self, filepath: str) -> Dict[str, Any]:
        """
        Process an image file for visualization and calibration
        
        Args:
            filepath: Path to the image file
            
        Returns:
            Dictionary containing processed data or error information
        """
        try:
            # Load image using PIL
            image = Image.open(filepath)
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Get image properties
            width, height = image.size
            channels = len(image.getbands())
            
            # Save processed image (for web serving)
            processed_filename = f"processed_{os.path.basename(filepath)}"
            processed_path = os.path.join('processed', processed_filename)
            
            # Ensure processed directory exists
            os.makedirs('processed', exist_ok=True)
            
            # Save as web-compatible format
            if not processed_filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                processed_filename = processed_filename.rsplit('.', 1)[0] + '.jpg'
                processed_path = os.path.join('processed', processed_filename)
            
            image.save(processed_path, 'JPEG' if processed_filename.endswith('.jpg') else 'PNG')
            
            # Also load with OpenCV for additional processing if needed
            cv_image = cv2.imread(filepath)
            cv_image_rgb = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
            
            processed_data = {
                'original_path': filepath,
                'processed_filename': processed_filename,
                'processed_path': processed_path,
                'dimensions': (width, height),
                'channels': channels,
                'image_array': cv_image_rgb,  # For internal processing
                'pil_image': image  # For web serving
            }
            
            return {
                'success': True,
                'data': processed_data,
                'dimensions': (width, height),
                'channels': channels
            }
            
        except Exception as e:
            logger.error(f"Error processing image file {filepath}: {str(e)}")
            return {
                'success': False,
                'error': f'Failed to process image file: {str(e)}'
            }
    
    def get_pixel_coordinates(self, image_data: Dict[str, Any], x: int, y: int) -> Tuple[int, int]:
        """
        Get pixel coordinates, ensuring they're within image bounds
        
        Args:
            image_data: Processed image data
            x, y: Pixel coordinates
            
        Returns:
            Validated pixel coordinates
        """
        width, height = image_data['dimensions']
        x = max(0, min(x, width - 1))
        y = max(0, min(y, height - 1))
        return x, y
    
    def get_pixel_value(self, image_data: Dict[str, Any], x: int, y: int) -> Tuple[int, int, int]:
        """
        Get RGB pixel value at specified coordinates
        
        Args:
            image_data: Processed image data
            x, y: Pixel coordinates
            
        Returns:
            RGB pixel values
        """
        x, y = self.get_pixel_coordinates(image_data, x, y)
        image_array = image_data['image_array']
        return tuple(image_array[y, x])  # Note: array indexing is [y, x]
