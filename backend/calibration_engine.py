"""
Calibration engine for computing extrinsic parameters between sensors
"""

import numpy as np
import cv2
from scipy.optimize import least_squares
from typing import Dict, Any, List, Tuple, Optional
import logging
import json

logger = logging.getLogger(__name__)

class CalibrationEngine:
    """Handles sensor calibration computations and correspondence management"""
    
    def __init__(self):
        self.correspondences = {}  # Store correspondences by session
        self.calibration_results = {}  # Store calibration results
        
    def add_correspondence(self, pointcloud_id: str, image_id: str, 
                          pcd_point: List[float], image_point: List[float]) -> Dict[str, Any]:
        """
        Add a correspondence point between point cloud and image
        
        Args:
            pointcloud_id: ID of the point cloud
            image_id: ID of the image
            pcd_point: 3D point coordinates [x, y, z]
            image_point: 2D image coordinates [u, v]
            
        Returns:
            Result dictionary with success status and correspondence info
        """
        try:
            # Create session key
            session_key = f"{pointcloud_id}_{image_id}"
            
            if session_key not in self.correspondences:
                self.correspondences[session_key] = {
                    'pointcloud_id': pointcloud_id,
                    'image_id': image_id,
                    'points_3d': [],
                    'points_2d': [],
                    'correspondence_count': 0
                }
            
            # Validate input points
            if len(pcd_point) != 3:
                return {'success': False, 'error': 'PCD point must have 3 coordinates'}
            
            if len(image_point) != 2:
                return {'success': False, 'error': 'Image point must have 2 coordinates'}
            
            # Add correspondence
            self.correspondences[session_key]['points_3d'].append(pcd_point)
            self.correspondences[session_key]['points_2d'].append(image_point)
            self.correspondences[session_key]['correspondence_count'] += 1
            
            return {
                'success': True,
                'session_key': session_key,
                'correspondence_count': self.correspondences[session_key]['correspondence_count'],
                'message': f'Added correspondence point. Total: {self.correspondences[session_key]["correspondence_count"]}'
            }
            
        except Exception as e:
            logger.error(f"Error adding correspondence: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def remove_correspondence(self, session_key: str, index: int) -> Dict[str, Any]:
        """
        Remove a correspondence point by index
        
        Args:
            session_key: Session identifier
            index: Index of correspondence to remove
            
        Returns:
            Result dictionary
        """
        try:
            if session_key not in self.correspondences:
                return {'success': False, 'error': 'Session not found'}
            
            correspondences = self.correspondences[session_key]
            
            if index < 0 or index >= len(correspondences['points_3d']):
                return {'success': False, 'error': 'Invalid correspondence index'}
            
            # Remove correspondence
            correspondences['points_3d'].pop(index)
            correspondences['points_2d'].pop(index)
            correspondences['correspondence_count'] -= 1
            
            return {
                'success': True,
                'correspondence_count': correspondences['correspondence_count'],
                'message': f'Removed correspondence. Remaining: {correspondences["correspondence_count"]}'
            }
            
        except Exception as e:
            logger.error(f"Error removing correspondence: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def get_correspondences(self, pointcloud_id: str, image_id: str) -> Dict[str, Any]:
        """
        Get all correspondences for a session
        
        Args:
            pointcloud_id: ID of the point cloud
            image_id: ID of the image
            
        Returns:
            Correspondences data
        """
        session_key = f"{pointcloud_id}_{image_id}"
        
        if session_key not in self.correspondences:
            return {
                'success': True,
                'correspondences': [],
                'count': 0
            }
        
        correspondences = self.correspondences[session_key]
        
        return {
            'success': True,
            'correspondences': [
                {
                    'index': i,
                    'pcd_point': correspondences['points_3d'][i],
                    'image_point': correspondences['points_2d'][i]
                }
                for i in range(len(correspondences['points_3d']))
            ],
            'count': correspondences['correspondence_count']
        }
    
    def compute_calibration(self, pointcloud_id: str, image_id: str, 
                           camera_matrix: Optional[np.ndarray] = None,
                           dist_coeffs: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        Compute calibration parameters from correspondence points
        
        Args:
            pointcloud_id: ID of the point cloud
            image_id: ID of the image
            camera_matrix: Camera intrinsic matrix (3x3)
            dist_coeffs: Distortion coefficients
            
        Returns:
            Calibration results
        """
        try:
            session_key = f"{pointcloud_id}_{image_id}"
            
            if session_key not in self.correspondences:
                return {'success': False, 'error': 'No correspondences found for this session'}
            
            correspondences = self.correspondences[session_key]
            
            if correspondences['correspondence_count'] < 4:
                return {
                    'success': False, 
                    'error': f'At least 4 correspondences required, got {correspondences["correspondence_count"]}'
                }
            
            # Convert to numpy arrays
            points_3d = np.array(correspondences['points_3d'], dtype=np.float32)
            points_2d = np.array(correspondences['points_2d'], dtype=np.float32)
            
            # Use default camera matrix if not provided
            if camera_matrix is None:
                # Assume reasonable default camera parameters
                # This should ideally be provided by the user or estimated
                # Use image center as principal point if available
                img_width, img_height = 640, 480  # Default image size
                camera_matrix = np.array([
                    [800, 0, img_width/2],
                    [0, 800, img_height/2],
                    [0, 0, 1]
                ], dtype=np.float32)
            
            if dist_coeffs is None:
                dist_coeffs = np.zeros(4, dtype=np.float32)
            
            # Solve PnP problem to get rotation and translation
            success, rvec, tvec, inliers = cv2.solvePnPRansac(
                points_3d, points_2d, camera_matrix, dist_coeffs,
                confidence=0.99, reprojectionError=5.0
            )
            
            if not success:
                return {'success': False, 'error': 'PnP solver failed to find solution'}
            
            # Convert rotation vector to rotation matrix
            rotation_matrix, _ = cv2.Rodrigues(rvec)
            
            # Create transformation matrix
            transformation_matrix = np.eye(4)
            transformation_matrix[:3, :3] = rotation_matrix
            transformation_matrix[:3, 3] = tvec.flatten()
            
            # Calculate reprojection error
            projected_points, _ = cv2.projectPoints(
                points_3d, rvec, tvec, camera_matrix, dist_coeffs
            )
            projected_points = projected_points.reshape(-1, 2)
            
            reprojection_errors = np.linalg.norm(points_2d - projected_points, axis=1)
            mean_error = np.mean(reprojection_errors)
            max_error = np.max(reprojection_errors)
            
            # Store calibration results
            calibration_result = {
                'session_key': session_key,
                'pointcloud_id': pointcloud_id,
                'image_id': image_id,
                'transformation_matrix': transformation_matrix,
                'rotation_matrix': rotation_matrix,
                'translation_vector': tvec.flatten(),
                'rotation_vector': rvec.flatten(),
                'camera_matrix': camera_matrix,
                'distortion_coefficients': dist_coeffs,
                'reprojection_error_mean': float(mean_error),
                'reprojection_error_max': float(max_error),
                'inlier_count': len(inliers) if inliers is not None else correspondences['correspondence_count'],
                'total_correspondences': correspondences['correspondence_count'],
                'success': True
            }
            
            self.calibration_results[session_key] = calibration_result
            
            return {
                'success': True,
                'calibration_id': session_key,
                'transformation_matrix': transformation_matrix.tolist(),
                'rotation_matrix': rotation_matrix.tolist(),
                'translation_vector': tvec.flatten().tolist(),
                'reprojection_error_mean': float(mean_error),
                'reprojection_error_max': float(max_error),
                'inlier_count': len(inliers) if inliers is not None else correspondences['correspondence_count'],
                'total_correspondences': correspondences['correspondence_count']
            }
            
        except Exception as e:
            logger.error(f"Error computing calibration: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def refine_calibration(self, session_key: str, max_iterations: int = 100) -> Dict[str, Any]:
        """
        Refine calibration using iterative optimization
        
        Args:
            session_key: Session identifier
            max_iterations: Maximum optimization iterations
            
        Returns:
            Refined calibration results
        """
        try:
            if session_key not in self.calibration_results:
                return {'success': False, 'error': 'No calibration found for refinement'}
            
            if session_key not in self.correspondences:
                return {'success': False, 'error': 'No correspondences found'}
            
            correspondences = self.correspondences[session_key]
            calibration = self.calibration_results[session_key]
            
            points_3d = np.array(correspondences['points_3d'], dtype=np.float32)
            points_2d = np.array(correspondences['points_2d'], dtype=np.float32)
            
            # Initial parameters: rotation vector + translation vector
            initial_params = np.concatenate([
                calibration['rotation_vector'],
                calibration['translation_vector']
            ])
            
            # Optimization function
            def residual_function(params):
                rvec = params[:3]
                tvec = params[3:6]
                
                projected_points, _ = cv2.projectPoints(
                    points_3d, rvec, tvec, 
                    calibration['camera_matrix'], 
                    calibration['distortion_coefficients']
                )
                projected_points = projected_points.reshape(-1, 2)
                
                residuals = (points_2d - projected_points).flatten()
                return residuals
            
            # Perform optimization
            result = least_squares(
                residual_function, 
                initial_params,
                max_nfev=max_iterations
            )
            
            if result.success:
                # Extract optimized parameters
                optimized_rvec = result.x[:3]
                optimized_tvec = result.x[3:6]
                
                # Convert to rotation matrix
                optimized_rotation_matrix, _ = cv2.Rodrigues(optimized_rvec)
                
                # Create transformation matrix
                optimized_transformation_matrix = np.eye(4)
                optimized_transformation_matrix[:3, :3] = optimized_rotation_matrix
                optimized_transformation_matrix[:3, 3] = optimized_tvec
                
                # Calculate final reprojection error
                projected_points, _ = cv2.projectPoints(
                    points_3d, optimized_rvec, optimized_tvec,
                    calibration['camera_matrix'], 
                    calibration['distortion_coefficients']
                )
                projected_points = projected_points.reshape(-1, 2)
                
                reprojection_errors = np.linalg.norm(points_2d - projected_points, axis=1)
                refined_mean_error = np.mean(reprojection_errors)
                refined_max_error = np.max(reprojection_errors)
                
                # Update calibration results
                self.calibration_results[session_key].update({
                    'transformation_matrix': optimized_transformation_matrix,
                    'rotation_matrix': optimized_rotation_matrix,
                    'translation_vector': optimized_tvec,
                    'rotation_vector': optimized_rvec,
                    'reprojection_error_mean': float(refined_mean_error),
                    'reprojection_error_max': float(refined_max_error),
                    'refined': True,
                    'optimization_iterations': result.nfev
                })
                
                return {
                    'success': True,
                    'transformation_matrix': optimized_transformation_matrix.tolist(),
                    'reprojection_error_mean': float(refined_mean_error),
                    'reprojection_error_max': float(refined_max_error),
                    'optimization_iterations': result.nfev,
                    'improvement': float(calibration['reprojection_error_mean'] - refined_mean_error)
                }
            else:
                return {'success': False, 'error': 'Optimization failed to converge'}
                
        except Exception as e:
            logger.error(f"Error refining calibration: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def export_calibration(self, calibration_id: str, format: str = 'json') -> Dict[str, Any]:
        """
        Export calibration parameters in specified format
        
        Args:
            calibration_id: Calibration session identifier
            format: Export format ('json', 'yaml', 'txt')
            
        Returns:
            Export result with file path or data
        """
        try:
            if calibration_id not in self.calibration_results:
                return {'success': False, 'error': 'Calibration not found'}
            
            calibration = self.calibration_results[calibration_id]
            
            # Prepare export data
            export_data = {
                'calibration_info': {
                    'pointcloud_id': calibration['pointcloud_id'],
                    'image_id': calibration['image_id'],
                    'timestamp': calibration.get('timestamp', 'unknown'),
                    'total_correspondences': calibration['total_correspondences'],
                    'inlier_count': calibration['inlier_count']
                },
                'transformation_matrix': calibration['transformation_matrix'].tolist(),
                'rotation_matrix': calibration['rotation_matrix'].tolist(),
                'translation_vector': calibration['translation_vector'].tolist(),
                'rotation_vector': calibration['rotation_vector'].tolist(),
                'camera_matrix': calibration['camera_matrix'].tolist(),
                'distortion_coefficients': calibration['distortion_coefficients'].tolist(),
                'quality_metrics': {
                    'reprojection_error_mean': calibration['reprojection_error_mean'],
                    'reprojection_error_max': calibration['reprojection_error_max'],
                    'refined': calibration.get('refined', False)
                }
            }
            
            if format.lower() == 'json':
                export_filename = f'calibration_{calibration_id}.json'
                export_path = f'data/calibrations/{export_filename}'
                
                with open(export_path, 'w') as f:
                    json.dump(export_data, f, indent=2)
                
                return {
                    'success': True,
                    'export_path': export_path,
                    'format': 'json',
                    'data': export_data
                }
            
            else:
                return {'success': False, 'error': f'Unsupported export format: {format}'}
                
        except Exception as e:
            logger.error(f"Error exporting calibration: {str(e)}")
            return {'success': False, 'error': str(e)}
