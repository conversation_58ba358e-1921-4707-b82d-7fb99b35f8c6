"""
Data management module for storing and retrieving processed data
"""

import uuid
import numpy as np
import json
import os
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class DataManager:
    """Manages storage and retrieval of processed point cloud and image data"""
    
    def __init__(self):
        self.pointcloud_data = {}  # In-memory storage for point cloud data
        self.image_data = {}       # In-memory storage for image metadata
        self.calibration_data = {} # Storage for calibration sessions
        
        # Ensure data directories exist
        os.makedirs('data/pointclouds', exist_ok=True)
        os.makedirs('data/images', exist_ok=True)
        os.makedirs('data/calibrations', exist_ok=True)
    
    def store_pointcloud_data(self, data: Dict[str, Any]) -> str:
        """
        Store processed point cloud data and return a unique identifier
        
        Args:
            data: Processed point cloud data dictionary
            
        Returns:
            Unique data identifier
        """
        data_id = str(uuid.uuid4())
        
        # Store in memory for quick access
        self.pointcloud_data[data_id] = data
        
        # Also save to disk for persistence (optional)
        try:
            # Save points and colors as numpy arrays
            np.savez_compressed(
                f'data/pointclouds/{data_id}.npz',
                points=data['points'],
                colors=data['colors'],
                intensities=data['intensities'] if data['intensities'] is not None else np.array([]),
                normals=data['normals'] if data['normals'] is not None else np.array([])
            )
            
            # Save metadata as JSON
            metadata = {
                'data_id': data_id,
                'point_count': data['point_count'],
                'bounds': data['bounds'],
                'has_colors': data['has_colors'],
                'has_normals': data['has_normals'],
                'has_intensity': data['has_intensity']
            }
            
            with open(f'data/pointclouds/{data_id}_metadata.json', 'w') as f:
                json.dump(metadata, f, indent=2)
                
        except Exception as e:
            logger.warning(f"Could not save point cloud data to disk: {str(e)}")
        
        return data_id
    
    def get_pointcloud_data(self, data_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve point cloud data by ID
        
        Args:
            data_id: Unique data identifier
            
        Returns:
            Point cloud data dictionary or None if not found
        """
        # Try memory first
        if data_id in self.pointcloud_data:
            return self.pointcloud_data[data_id]
        
        # Try loading from disk
        try:
            npz_path = f'data/pointclouds/{data_id}.npz'
            metadata_path = f'data/pointclouds/{data_id}_metadata.json'
            
            if os.path.exists(npz_path) and os.path.exists(metadata_path):
                # Load numpy data
                npz_data = np.load(npz_path)
                
                # Load metadata
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                
                # Reconstruct data dictionary
                data = {
                    'points': npz_data['points'],
                    'colors': npz_data['colors'],
                    'intensities': npz_data['intensities'] if npz_data['intensities'].size > 0 else None,
                    'normals': npz_data['normals'] if npz_data['normals'].size > 0 else None,
                    'point_count': metadata['point_count'],
                    'bounds': metadata['bounds'],
                    'has_colors': metadata['has_colors'],
                    'has_normals': metadata['has_normals'],
                    'has_intensity': metadata['has_intensity']
                }
                
                # Store in memory for future access
                self.pointcloud_data[data_id] = data
                
                return data
                
        except Exception as e:
            logger.error(f"Error loading point cloud data from disk: {str(e)}")
        
        return None
    
    def store_image_data(self, data: Dict[str, Any]) -> str:
        """
        Store processed image data and return a unique identifier
        
        Args:
            data: Processed image data dictionary
            
        Returns:
            Unique data identifier
        """
        data_id = str(uuid.uuid4())
        
        # Store metadata (actual image is already saved to processed folder)
        image_metadata = {
            'data_id': data_id,
            'original_path': data['original_path'],
            'processed_filename': data['processed_filename'],
            'processed_path': data['processed_path'],
            'dimensions': data['dimensions'],
            'channels': data['channels']
        }
        
        self.image_data[data_id] = image_metadata
        
        # Save metadata to disk
        try:
            with open(f'data/images/{data_id}_metadata.json', 'w') as f:
                json.dump(image_metadata, f, indent=2)
        except Exception as e:
            logger.warning(f"Could not save image metadata to disk: {str(e)}")
        
        return data_id
    
    def get_image_data(self, data_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve image data by ID
        
        Args:
            data_id: Unique data identifier
            
        Returns:
            Image data dictionary or None if not found
        """
        # Try memory first
        if data_id in self.image_data:
            return self.image_data[data_id]
        
        # Try loading from disk
        try:
            metadata_path = f'data/images/{data_id}_metadata.json'
            
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                
                # Store in memory for future access
                self.image_data[data_id] = metadata
                
                return metadata
                
        except Exception as e:
            logger.error(f"Error loading image metadata from disk: {str(e)}")
        
        return None
    
    def create_calibration_session(self, pointcloud_id: str, image_id: str) -> str:
        """
        Create a new calibration session
        
        Args:
            pointcloud_id: ID of the point cloud data
            image_id: ID of the image data
            
        Returns:
            Calibration session ID
        """
        session_id = str(uuid.uuid4())
        
        calibration_session = {
            'session_id': session_id,
            'pointcloud_id': pointcloud_id,
            'image_id': image_id,
            'correspondences': [],  # List of point correspondences
            'calibration_matrix': None,
            'transformation_matrix': None,
            'calibration_error': None,
            'status': 'active'
        }
        
        self.calibration_data[session_id] = calibration_session
        
        # Save to disk
        try:
            with open(f'data/calibrations/{session_id}.json', 'w') as f:
                json.dump(calibration_session, f, indent=2, default=self._json_serializer)
        except Exception as e:
            logger.warning(f"Could not save calibration session to disk: {str(e)}")
        
        return session_id
    
    def get_calibration_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve calibration session by ID
        
        Args:
            session_id: Calibration session ID
            
        Returns:
            Calibration session data or None if not found
        """
        # Try memory first
        if session_id in self.calibration_data:
            return self.calibration_data[session_id]
        
        # Try loading from disk
        try:
            session_path = f'data/calibrations/{session_id}.json'
            
            if os.path.exists(session_path):
                with open(session_path, 'r') as f:
                    session_data = json.load(f)
                
                # Convert numpy arrays back from lists
                if session_data.get('calibration_matrix'):
                    session_data['calibration_matrix'] = np.array(session_data['calibration_matrix'])
                if session_data.get('transformation_matrix'):
                    session_data['transformation_matrix'] = np.array(session_data['transformation_matrix'])
                
                # Store in memory for future access
                self.calibration_data[session_id] = session_data
                
                return session_data
                
        except Exception as e:
            logger.error(f"Error loading calibration session from disk: {str(e)}")
        
        return None
    
    def update_calibration_session(self, session_id: str, updates: Dict[str, Any]) -> bool:
        """
        Update calibration session data
        
        Args:
            session_id: Calibration session ID
            updates: Dictionary of updates to apply
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if session_id not in self.calibration_data:
                session = self.get_calibration_session(session_id)
                if session is None:
                    return False
            
            # Apply updates
            self.calibration_data[session_id].update(updates)
            
            # Save to disk
            with open(f'data/calibrations/{session_id}.json', 'w') as f:
                json.dump(self.calibration_data[session_id], f, indent=2, default=self._json_serializer)
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating calibration session: {str(e)}")
            return False
    
    def _json_serializer(self, obj):
        """Custom JSON serializer for numpy arrays"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
    
    def list_pointclouds(self) -> Dict[str, Dict[str, Any]]:
        """List all available point cloud data"""
        result = {}
        
        # From memory
        for data_id, data in self.pointcloud_data.items():
            result[data_id] = {
                'point_count': data['point_count'],
                'bounds': data['bounds'],
                'has_intensity': data['has_intensity']
            }
        
        # From disk (if not already in memory)
        try:
            for filename in os.listdir('data/pointclouds'):
                if filename.endswith('_metadata.json'):
                    data_id = filename.replace('_metadata.json', '')
                    if data_id not in result:
                        with open(f'data/pointclouds/{filename}', 'r') as f:
                            metadata = json.load(f)
                            result[data_id] = {
                                'point_count': metadata['point_count'],
                                'bounds': metadata['bounds'],
                                'has_intensity': metadata['has_intensity']
                            }
        except Exception as e:
            logger.warning(f"Error listing point clouds from disk: {str(e)}")
        
        return result
    
    def list_images(self) -> Dict[str, Dict[str, Any]]:
        """List all available image data"""
        result = {}
        
        # From memory
        for data_id, data in self.image_data.items():
            result[data_id] = {
                'dimensions': data['dimensions'],
                'channels': data['channels'],
                'processed_filename': data['processed_filename']
            }
        
        # From disk (if not already in memory)
        try:
            for filename in os.listdir('data/images'):
                if filename.endswith('_metadata.json'):
                    data_id = filename.replace('_metadata.json', '')
                    if data_id not in result:
                        with open(f'data/images/{filename}', 'r') as f:
                            metadata = json.load(f)
                            result[data_id] = {
                                'dimensions': metadata['dimensions'],
                                'channels': metadata['channels'],
                                'processed_filename': metadata['processed_filename']
                            }
        except Exception as e:
            logger.warning(f"Error listing images from disk: {str(e)}")
        
        return result
