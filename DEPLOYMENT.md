# Deployment Guide

This document provides instructions for deploying the Web-based Sensor Calibration Tool in various environments.

## Quick Start (Development)

### Prerequisites
- Python 3.8 or higher
- Modern web browser with WebGL support

### Installation
1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd web-calib
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Generate sample data (optional):
   ```bash
   python generate_sample_data.py
   ```

4. Start the application:
   ```bash
   python run.py
   ```

5. Open http://localhost:5000 in your browser

## Docker Deployment

### Build and Run with Docker
```bash
# Build the image
docker build -t web-calib .

# Run the container
docker run -p 5000:5000 -v $(pwd)/data:/app/data web-calib
```

### Using Docker Compose
```bash
# Development mode
docker-compose up

# Production mode with nginx
docker-compose --profile production up
```

## Production Deployment

### Environment Variables
Set these environment variables for production:

```bash
export FLASK_ENV=production
export FLASK_DEBUG=0
export SECRET_KEY=your-secret-key-here
```

### Nginx Configuration
For production deployment with nginx, create `/etc/nginx/sites-available/web-calib`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    client_max_body_size 500M;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Increase timeout for large file uploads
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # Serve static files directly
    location /static/ {
        alias /path/to/web-calib/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### Systemd Service
Create `/etc/systemd/system/web-calib.service`:

```ini
[Unit]
Description=Web-based Sensor Calibration Tool
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/web-calib
Environment=FLASK_ENV=production
Environment=FLASK_DEBUG=0
ExecStart=/usr/bin/python3 app.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start the service:
```bash
sudo systemctl enable web-calib
sudo systemctl start web-calib
```

## Cloud Deployment

### AWS EC2
1. Launch an EC2 instance (Ubuntu 20.04 LTS recommended)
2. Install Docker and Docker Compose
3. Clone the repository
4. Run with Docker Compose
5. Configure security groups to allow HTTP/HTTPS traffic

### Google Cloud Platform
1. Create a Compute Engine instance
2. Install dependencies
3. Deploy using Docker or direct Python execution
4. Configure firewall rules

### Azure
1. Create a Virtual Machine
2. Install Docker
3. Deploy using Docker Compose
4. Configure Network Security Groups

## Performance Optimization

### For Large Point Clouds
- Increase server memory allocation
- Consider point cloud downsampling for visualization
- Use SSD storage for faster file I/O

### For Multiple Users
- Use a proper WSGI server (gunicorn, uWSGI)
- Implement session management
- Add load balancing with nginx

### Example Gunicorn Configuration
```bash
gunicorn --bind 0.0.0.0:5000 --workers 4 --timeout 300 app:app
```

## Security Considerations

### File Upload Security
- Validate file types and sizes
- Scan uploaded files for malware
- Use secure file storage locations
- Implement user authentication if needed

### Network Security
- Use HTTPS in production
- Implement rate limiting
- Configure firewall rules
- Regular security updates

## Monitoring and Logging

### Application Logs
Logs are written to stdout/stderr. In production, redirect to files:
```bash
python app.py > /var/log/web-calib/app.log 2>&1
```

### Health Checks
The application provides a basic health check endpoint:
```bash
curl http://localhost:5000/api/health
```

### Monitoring Tools
Consider using:
- Prometheus + Grafana for metrics
- ELK stack for log analysis
- Uptime monitoring services

## Backup and Recovery

### Data Backup
Important directories to backup:
- `data/` - Application data and calibration results
- `uploads/` - User uploaded files
- `processed/` - Processed files

### Backup Script Example
```bash
#!/bin/bash
BACKUP_DIR="/backup/web-calib/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

cp -r data/ "$BACKUP_DIR/"
cp -r uploads/ "$BACKUP_DIR/"
cp -r processed/ "$BACKUP_DIR/"

# Compress backup
tar -czf "$BACKUP_DIR.tar.gz" -C /backup/web-calib "$(basename $BACKUP_DIR)"
rm -rf "$BACKUP_DIR"
```

## Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Find process using port 5000
   lsof -i :5000
   # Kill the process or use a different port
   python run.py --port 5001
   ```

2. **Permission denied errors**
   ```bash
   # Fix file permissions
   chmod -R 755 uploads/ processed/ data/
   ```

3. **Out of memory errors**
   - Reduce point cloud size
   - Increase system memory
   - Use point cloud downsampling

4. **WebGL not supported**
   - Update browser
   - Enable hardware acceleration
   - Use a different browser

### Log Analysis
Check application logs for errors:
```bash
# If running with systemd
journalctl -u web-calib -f

# If running directly
tail -f /var/log/web-calib/app.log
```

## Scaling

### Horizontal Scaling
- Use multiple application instances
- Implement load balancing
- Share data storage between instances

### Vertical Scaling
- Increase CPU and memory
- Use faster storage (SSD)
- Optimize database queries

## Updates and Maintenance

### Updating the Application
1. Stop the service
2. Backup data
3. Pull latest code
4. Update dependencies
5. Restart the service

### Database Maintenance
- Regular cleanup of old files
- Monitor disk usage
- Implement data retention policies

## Support

For deployment issues:
1. Check the troubleshooting section
2. Review application logs
3. Verify system requirements
4. Test with sample data

For production deployments, consider:
- Professional support services
- Custom deployment assistance
- Performance optimization consulting
