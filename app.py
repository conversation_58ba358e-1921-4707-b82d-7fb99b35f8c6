"""
Web-based Sensor Calibration Tool
Main Flask application for handling file uploads, processing, and serving calibration interface
"""

from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import os
import json
import numpy as np
from werkzeug.utils import secure_filename
import logging

# Import our custom modules
from backend.file_processors import PCDProcessor, ImageProcessor
from backend.calibration_engine import CalibrationEngine
from backend.data_manager import DataManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Configuration
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['PROCESSED_FOLDER'] = 'processed'

# Ensure upload directories exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['PROCESSED_FOLDER'], exist_ok=True)

# Initialize processors and managers
pcd_processor = PCDProcessor()
image_processor = ImageProcessor()
calibration_engine = CalibrationEngine()
data_manager = DataManager()

# Allowed file extensions
ALLOWED_PCD_EXTENSIONS = {'pcd'}
ALLOWED_IMAGE_EXTENSIONS = {'png', 'jpg', 'jpeg', 'bmp', 'tiff', 'tif'}

def allowed_file(filename, allowed_extensions):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

@app.route('/')
def index():
    """Serve the main calibration interface"""
    return render_template('index.html')

@app.route('/api/upload/pcd', methods=['POST'])
def upload_pcd():
    """Handle PCD file upload and processing"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename, ALLOWED_PCD_EXTENSIONS):
            return jsonify({'error': 'Invalid file type. Only PCD files allowed'}), 400
        
        # Save uploaded file
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Process PCD file
        result = pcd_processor.process_file(filepath)
        
        if result['success']:
            # Store processed data
            data_id = data_manager.store_pointcloud_data(result['data'])
            
            return jsonify({
                'success': True,
                'data_id': data_id,
                'filename': filename,
                'point_count': result['point_count'],
                'bounds': result['bounds'],
                'has_intensity': result['has_intensity']
            })
        else:
            return jsonify({'error': result['error']}), 500
            
    except Exception as e:
        logger.error(f"Error processing PCD upload: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/upload/image', methods=['POST'])
def upload_image():
    """Handle image file upload and processing"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename, ALLOWED_IMAGE_EXTENSIONS):
            return jsonify({'error': 'Invalid file type. Only image files allowed'}), 400
        
        # Save uploaded file
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Process image file
        result = image_processor.process_file(filepath)
        
        if result['success']:
            # Store processed data
            data_id = data_manager.store_image_data(result['data'])
            
            return jsonify({
                'success': True,
                'data_id': data_id,
                'filename': filename,
                'dimensions': result['dimensions'],
                'channels': result['channels']
            })
        else:
            return jsonify({'error': result['error']}), 500
            
    except Exception as e:
        logger.error(f"Error processing image upload: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/pointcloud/<data_id>')
def get_pointcloud_data(data_id):
    """Retrieve processed point cloud data for visualization"""
    try:
        data = data_manager.get_pointcloud_data(data_id)
        if data is None:
            return jsonify({'error': 'Point cloud data not found'}), 404
        
        return jsonify({
            'success': True,
            'points': data['points'].tolist(),
            'colors': data['colors'].tolist() if 'colors' in data else None,
            'intensities': data['intensities'].tolist() if 'intensities' in data else None
        })
    except Exception as e:
        logger.error(f"Error retrieving point cloud data: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/image/<data_id>')
def get_image_data(data_id):
    """Retrieve processed image data"""
    try:
        data = data_manager.get_image_data(data_id)
        if data is None:
            return jsonify({'error': 'Image data not found'}), 404
        
        return send_from_directory(app.config['PROCESSED_FOLDER'], data['processed_filename'])
    except Exception as e:
        logger.error(f"Error retrieving image data: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/calibration/points', methods=['POST'])
def add_calibration_point():
    """Add corresponding points for calibration"""
    try:
        data = request.get_json()
        
        # Validate input
        required_fields = ['pointcloud_id', 'image_id', 'pcd_point', 'image_point']
        if not all(field in data for field in required_fields):
            return jsonify({'error': 'Missing required fields'}), 400
        
        # Add correspondence point
        result = calibration_engine.add_correspondence(
            data['pointcloud_id'],
            data['image_id'],
            data['pcd_point'],
            data['image_point']
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error adding calibration point: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/calibration/compute', methods=['POST'])
def compute_calibration():
    """Compute calibration parameters from correspondence points"""
    try:
        data = request.get_json()
        
        # Validate input
        if 'pointcloud_id' not in data or 'image_id' not in data:
            return jsonify({'error': 'Missing pointcloud_id or image_id'}), 400
        
        # Compute calibration
        result = calibration_engine.compute_calibration(
            data['pointcloud_id'],
            data['image_id']
        )
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error computing calibration: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/calibration/export', methods=['POST'])
def export_calibration():
    """Export calibration parameters"""
    try:
        data = request.get_json()
        
        if 'calibration_id' not in data:
            return jsonify({'error': 'Missing calibration_id'}), 400
        
        result = calibration_engine.export_calibration(data['calibration_id'])
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error exporting calibration: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
