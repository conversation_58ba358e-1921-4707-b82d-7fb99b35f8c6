#!/usr/bin/env python3
"""
Test script for the sensor calibration tool
Tests basic functionality of file processing and calibration components
"""

import os
import sys
import numpy as np
import tempfile
import json
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.file_processors import PCDProcessor, ImageProcessor
from backend.calibration_engine import CalibrationEngine
from backend.data_manager import DataManager

def create_test_pcd_file():
    """Create a simple test PCD file"""
    test_pcd_content = """# .PCD v0.7 - Point Cloud Data file format
VERSION 0.7
FIELDS x y z intensity
SIZE 4 4 4 4
TYPE F F F F
COUNT 1 1 1 1
WIDTH 100
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS 100
DATA ascii
"""
    
    # Generate some test points
    points = []
    for i in range(100):
        x = np.random.uniform(-10, 10)
        y = np.random.uniform(-10, 10)
        z = np.random.uniform(0, 5)
        intensity = np.random.uniform(0, 255)
        points.append(f"{x:.6f} {y:.6f} {z:.6f} {intensity:.1f}")
    
    test_pcd_content += "\n".join(points)
    
    # Write to temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.pcd', delete=False) as f:
        f.write(test_pcd_content)
        return f.name

def create_test_image():
    """Create a simple test image"""
    try:
        from PIL import Image
        import numpy as np
        
        # Create a simple test image
        width, height = 640, 480
        image_array = np.random.randint(0, 256, (height, width, 3), dtype=np.uint8)
        
        # Add some patterns for visual interest
        image_array[100:150, 100:150] = [255, 0, 0]  # Red square
        image_array[200:250, 200:250] = [0, 255, 0]  # Green square
        image_array[300:350, 300:350] = [0, 0, 255]  # Blue square
        
        image = Image.fromarray(image_array)
        
        # Save to temporary file
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
            image.save(f.name, 'PNG')
            return f.name
    except ImportError:
        print("PIL not available, skipping image test")
        return None

def test_pcd_processor():
    """Test PCD file processing"""
    print("Testing PCD Processor...")
    
    processor = PCDProcessor()
    test_file = create_test_pcd_file()
    
    try:
        result = processor.process_file(test_file)
        
        if result['success']:
            print(f"✓ PCD processing successful")
            print(f"  - Point count: {result['point_count']}")
            print(f"  - Has intensity: {result['has_intensity']}")
            print(f"  - Bounds: {result['bounds']}")
            return True
        else:
            print(f"✗ PCD processing failed: {result['error']}")
            return False
    finally:
        os.unlink(test_file)

def test_image_processor():
    """Test image file processing"""
    print("Testing Image Processor...")
    
    processor = ImageProcessor()
    test_file = create_test_image()
    
    if not test_file:
        print("⚠ Skipping image processor test (PIL not available)")
        return True
    
    try:
        result = processor.process_file(test_file)
        
        if result['success']:
            print(f"✓ Image processing successful")
            print(f"  - Dimensions: {result['dimensions']}")
            print(f"  - Channels: {result['channels']}")
            return True
        else:
            print(f"✗ Image processing failed: {result['error']}")
            return False
    finally:
        os.unlink(test_file)
        # Clean up processed file if it exists
        if result.get('success') and 'data' in result:
            processed_path = result['data'].get('processed_path')
            if processed_path and os.path.exists(processed_path):
                os.unlink(processed_path)

def test_calibration_engine():
    """Test calibration engine"""
    print("Testing Calibration Engine...")
    
    engine = CalibrationEngine()
    
    # Test adding correspondences
    test_correspondences = [
        ([1.0, 2.0, 3.0], [100, 200]),
        ([2.0, 3.0, 4.0], [150, 250]),
        ([3.0, 4.0, 5.0], [200, 300]),
        ([4.0, 5.0, 6.0], [250, 350]),
        ([5.0, 6.0, 7.0], [300, 400])
    ]
    
    pointcloud_id = "test_pcd"
    image_id = "test_img"
    
    # Add correspondences
    for i, (pcd_point, img_point) in enumerate(test_correspondences):
        result = engine.add_correspondence(pointcloud_id, image_id, pcd_point, img_point)
        if not result['success']:
            print(f"✗ Failed to add correspondence {i+1}: {result['error']}")
            return False
    
    print(f"✓ Added {len(test_correspondences)} correspondences")
    
    # Test calibration computation
    try:
        result = engine.compute_calibration(pointcloud_id, image_id)
        
        if result['success']:
            print(f"✓ Calibration computation successful")
            print(f"  - Mean reprojection error: {result['reprojection_error_mean']:.3f}")
            print(f"  - Max reprojection error: {result['reprojection_error_max']:.3f}")
            print(f"  - Inliers: {result['inlier_count']}/{result['total_correspondences']}")
            return True
        else:
            print(f"✗ Calibration computation failed: {result['error']}")
            return False
    except Exception as e:
        print(f"✗ Calibration computation error: {str(e)}")
        return False

def test_data_manager():
    """Test data manager"""
    print("Testing Data Manager...")
    
    manager = DataManager()
    
    # Test point cloud data storage
    test_pcd_data = {
        'points': np.random.rand(100, 3),
        'colors': np.random.rand(100, 3),
        'intensities': np.random.rand(100),
        'normals': None,
        'has_colors': True,
        'has_normals': False,
        'has_intensity': True,
        'bounds': {'min': [0, 0, 0], 'max': [1, 1, 1], 'center': [0.5, 0.5, 0.5]},
        'point_count': 100
    }
    
    # Store data
    pcd_id = manager.store_pointcloud_data(test_pcd_data)
    print(f"✓ Stored point cloud data with ID: {pcd_id}")
    
    # Retrieve data
    retrieved_data = manager.get_pointcloud_data(pcd_id)
    if retrieved_data is not None:
        print(f"✓ Retrieved point cloud data successfully")
        print(f"  - Point count: {retrieved_data['point_count']}")
    else:
        print(f"✗ Failed to retrieve point cloud data")
        return False
    
    # Test image data storage
    test_image_data = {
        'original_path': '/test/image.png',
        'processed_filename': 'processed_image.png',
        'processed_path': '/test/processed_image.png',
        'dimensions': (640, 480),
        'channels': 3
    }
    
    img_id = manager.store_image_data(test_image_data)
    print(f"✓ Stored image data with ID: {img_id}")
    
    # Retrieve image data
    retrieved_img_data = manager.get_image_data(img_id)
    if retrieved_img_data is not None:
        print(f"✓ Retrieved image data successfully")
        print(f"  - Dimensions: {retrieved_img_data['dimensions']}")
    else:
        print(f"✗ Failed to retrieve image data")
        return False
    
    return True

def test_integration():
    """Test integration between components"""
    print("Testing Component Integration...")
    
    # Create test files
    pcd_file = create_test_pcd_file()
    img_file = create_test_image()
    
    if not img_file:
        print("⚠ Skipping integration test (PIL not available)")
        os.unlink(pcd_file)
        return True
    
    try:
        # Initialize components
        pcd_processor = PCDProcessor()
        img_processor = ImageProcessor()
        data_manager = DataManager()
        calibration_engine = CalibrationEngine()
        
        # Process files
        pcd_result = pcd_processor.process_file(pcd_file)
        img_result = img_processor.process_file(img_file)
        
        if not pcd_result['success'] or not img_result['success']:
            print("✗ File processing failed")
            return False
        
        # Store data
        pcd_id = data_manager.store_pointcloud_data(pcd_result['data'])
        img_id = data_manager.store_image_data(img_result['data'])
        
        # Add some correspondences
        test_correspondences = [
            ([1.0, 2.0, 3.0], [100, 200]),
            ([2.0, 3.0, 4.0], [150, 250]),
            ([3.0, 4.0, 5.0], [200, 300]),
            ([4.0, 5.0, 6.0], [250, 350])
        ]
        
        for pcd_point, img_point in test_correspondences:
            calibration_engine.add_correspondence(pcd_id, img_id, pcd_point, img_point)
        
        # Compute calibration
        calib_result = calibration_engine.compute_calibration(pcd_id, img_id)
        
        if calib_result['success']:
            print("✓ Integration test successful")
            print(f"  - End-to-end processing completed")
            print(f"  - Calibration computed with error: {calib_result['reprojection_error_mean']:.3f}")
            return True
        else:
            print(f"✗ Integration test failed: {calib_result['error']}")
            return False
            
    finally:
        # Cleanup
        os.unlink(pcd_file)
        os.unlink(img_file)
        if img_result.get('success') and 'data' in img_result:
            processed_path = img_result['data'].get('processed_path')
            if processed_path and os.path.exists(processed_path):
                os.unlink(processed_path)

def main():
    """Run all tests"""
    print("=" * 50)
    print("Sensor Calibration Tool - Test Suite")
    print("=" * 50)
    
    tests = [
        ("PCD Processor", test_pcd_processor),
        ("Image Processor", test_image_processor),
        ("Calibration Engine", test_calibration_engine),
        ("Data Manager", test_data_manager),
        ("Integration", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "PASS" if success else "FAIL"
        print(f"{test_name:20} : {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
