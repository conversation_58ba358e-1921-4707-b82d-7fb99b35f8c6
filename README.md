# Web-based Sensor Calibration Tool

A comprehensive web application for performing extrinsic parameter calibration between LiDAR sensors and cameras. This tool provides an intuitive interface for loading point cloud data (PCD format) and images, selecting correspondence points, and computing calibration parameters.

## Features

### File Support
- **Point Cloud Files**: PCD format with support for intensity data
- **Image Files**: JPG, PNG, BMP, TIFF formats
- Automatic file processing and optimization for web display

### Visualization
- **3D Point Cloud Viewer**: 
  - Interactive Three.js-based visualization
  - Intensity-based color rendering
  - Pan, rotate, and zoom controls
  - Point selection with visual feedback
- **Image Viewer**:
  - Zoom and pan functionality
  - Point selection with coordinate display
  - Real-time coordinate feedback

### Calibration Features
- **Interactive Point Selection**: Click-based correspondence point selection
- **Real-time Feedback**: Visual confirmation of selected points
- **Calibration Computation**: PnP-based extrinsic parameter estimation
- **Quality Metrics**: Reprojection error analysis
- **Export Functionality**: JSON format calibration parameter export

## Installation

### Prerequisites
- Python 3.8 or higher
- Modern web browser with WebGL support

### Setup
1. Clone or download the project files
2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Run the application:
   ```bash
   python app.py
   ```

4. Open your web browser and navigate to:
   ```
   http://localhost:5000
   ```

## Usage

### 1. Load Data Files
- Click "Point Cloud (PCD)" to upload a PCD file
- Click "Image" to upload an image file
- Wait for files to process and display

### 2. Select Correspondence Points
- Switch to "Point Selection Mode" using the radio button
- Click on a point in the 3D point cloud
- Click on the corresponding point in the image
- Repeat to create multiple correspondences (minimum 4 required)

### 3. Compute Calibration
- Ensure you have at least 4 correspondence points
- Click "Compute Calibration"
- Review the calibration results and quality metrics

### 4. Export Results
- Click "Export Calibration" to download results as JSON
- The export includes transformation matrix, quality metrics, and metadata

## Interface Controls

### Visualization Controls
- **Point Size**: Adjust point cloud point size (1-10)
- **Show Intensity Colors**: Toggle intensity-based coloring
- **Show Coordinate Axes**: Toggle 3D coordinate axes display

### Interaction Modes
- **Point Selection Mode**: Click to select correspondence points
- **View Mode**: Pan, zoom, and rotate for data exploration

### Keyboard Shortcuts
- **Ctrl+R**: Reset all views to default
- **Ctrl+S**: Export calibration results
- **Ctrl+C**: Clear all correspondence points
- **Space**: Toggle between selection and view modes

## Technical Architecture

### Backend (Python)
- **Flask**: Web framework for API endpoints
- **Open3D**: Point cloud processing and PCD file handling
- **OpenCV**: Computer vision algorithms for calibration
- **NumPy/SciPy**: Numerical computations
- **PIL**: Image processing

### Frontend (JavaScript)
- **Three.js**: 3D visualization and point cloud rendering
- **HTML5 Canvas**: 2D image display and interaction
- **Modern ES6+**: Modular JavaScript architecture
- **CSS3**: Responsive design and styling

### API Endpoints
- `POST /api/upload/pcd`: Upload and process PCD files
- `POST /api/upload/image`: Upload and process image files
- `GET /api/pointcloud/<id>`: Retrieve processed point cloud data
- `GET /api/image/<id>`: Retrieve processed image
- `POST /api/calibration/points`: Add correspondence points
- `POST /api/calibration/compute`: Compute calibration parameters
- `POST /api/calibration/export`: Export calibration results

## File Structure
```
web-calib/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── backend/              # Backend modules
│   ├── file_processors.py    # PCD and image processing
│   ├── calibration_engine.py # Calibration algorithms
│   └── data_manager.py       # Data storage management
├── templates/            # HTML templates
│   └── index.html           # Main application interface
├── static/              # Frontend assets
│   ├── css/
│   │   └── style.css        # Application styles
│   └── js/
│       ├── utils.js         # Utility functions
│       ├── pointcloud-viewer.js # 3D visualization
│       ├── image-viewer.js     # Image display
│       ├── calibration-manager.js # Calibration logic
│       └── main.js            # Application controller
├── uploads/             # Uploaded files storage
├── processed/           # Processed files storage
└── data/               # Application data
    ├── pointclouds/    # Point cloud data cache
    ├── images/         # Image metadata cache
    └── calibrations/   # Calibration results
```

## Calibration Algorithm

The tool uses a PnP (Perspective-n-Point) algorithm to compute extrinsic parameters:

1. **Correspondence Collection**: User selects matching points between 3D point cloud and 2D image
2. **PnP Solving**: OpenCV's `solvePnPRansac` computes rotation and translation
3. **Quality Assessment**: Reprojection error calculation for validation
4. **Refinement**: Optional iterative optimization for improved accuracy

## Data Formats

### Input Files
- **PCD Files**: Standard Point Cloud Data format with optional intensity
- **Images**: Common formats (JPG, PNG, BMP, TIFF)

### Output Format (JSON)
```json
{
  "transformation_matrix": [[4x4 matrix]],
  "rotation_matrix": [[3x3 matrix]],
  "translation_vector": [x, y, z],
  "quality_metrics": {
    "reprojection_error_mean": 0.0,
    "reprojection_error_max": 0.0,
    "inlier_count": 0
  }
}
```

## Browser Compatibility
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Troubleshooting

### Common Issues
1. **Large File Upload Failures**: Check file size limits (500MB max)
2. **WebGL Errors**: Ensure browser supports WebGL 2.0
3. **Point Cloud Not Displaying**: Verify PCD file format and content
4. **Calibration Fails**: Ensure minimum 4 correspondence points

### Performance Tips
- Use reasonably sized point clouds (< 1M points for smooth interaction)
- Optimize images for web (< 10MB recommended)
- Close other browser tabs for better performance

## Development

### Adding New Features
The modular architecture allows easy extension:
- Backend: Add new processors in `backend/` directory
- Frontend: Create new modules in `static/js/`
- API: Extend endpoints in `app.py`

### Testing
- Test with various PCD file formats and sizes
- Verify calibration accuracy with known ground truth
- Check browser compatibility across different devices

## License
This project is provided as-is for educational and research purposes.

## Support
For issues and questions, please check the troubleshooting section or review the code documentation.
