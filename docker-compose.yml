version: '3.8'

services:
  web-calib:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./uploads:/app/uploads
      - ./processed:/app/processed
      - ./data:/app/data
      - ./sample_data:/app/sample_data
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=0
    restart: unless-stopped
    
  # Optional: Add nginx for production deployment
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - web-calib
    restart: unless-stopped
    profiles:
      - production
